package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.ComplianceCheckResultDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.CheckComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.AddApproveDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.VehicleUpdateStatusDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH;

/**
 * <AUTHOR>
 * @Date 2020/3/17 15:05
 */
@Service
public class VehicleCommandServiceImpl implements VehicleCommandService {
    private static final Logger logger = LoggerFactory.getLogger(VehicleCommandServiceImpl.class);

    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private TmsPmsproductQueryService tmsPmsproductQueryService;
    @Autowired
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;
    @Autowired
    ModRecordRespository modRecordRespository;
    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private RecruitingCommandService recruitingCommandService;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private DrvRecruitingRepository recruitingRepository;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    TmsTransportApproveCommandService approveCommandService;
    @Autowired
    TmsTransportQconfig tmsTransportQconfig;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;
    @Autowired
    DrvVehRecruitingQueryService drvVehRecruitingQueryService;
    @Autowired
    AuthorizationCheckService authorizationCheckService;
    @Autowired
    private DriverQueryService driverQueryService;
    @Autowired
    private TransportGroupRepository transportGroupRepository;
    @Autowired
    OverseasQconfig overseasQconfig;

    @Autowired
    VehicleGlobalIdRecordRepository vehicleGlobalIdRecordRepository;

    @Autowired
    SupplierDayProductLineMigrationHelper supplierDayProductLineMigrationHelper;

    @Autowired
    QueryCategoryService queryCategoryService;

    @Autowired
    DriverGuideProxy driverGuidProxy;
    @Autowired
    NetCardCheckUtil netCardCheckUtil;

    @Autowired
    private OverageQConfig overageQConfig;
    @Autowired
    CommonConfig commonConfig;

    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Integer> addVehicle(VehicleAddSOARequestType vehicleAddSOARequestType, boolean hasDrv) {
        if (!tmsPmsproductQueryService.checkSkuIsExist(vehicleAddSOARequestType.getSupplierId())) {
            return Result.Builder.<Integer>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPleaseFinishSkuCreate))
                    .withData(-1)
                    .build();
        }

        try {
            VehVehiclePO vehVehiclePO = new VehVehiclePO();
            VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
            BeanUtils.copyProperties(vehicleAddSOARequestType, vehicleRecruitingPO);
            vehicleRecruitingPO.setVehicleFrom(TmsTransportConstant.VehicleFromEnum.Veh_MANUAL.getCode());
            vehicleRecruitingPO.setVehicleColorId(vehicleAddSOARequestType.getVehicleColorId().intValue());
            vehicleRecruitingPO.setVersionFlag(vehicleAddSOARequestType.getVersionFlag() == null?tmsTransportQconfig.getSystemVersionFlag():vehicleAddSOARequestType.getVersionFlag());
            vehicleRecruitingPO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(vehicleAddSOARequestType.getProLineList()));
            vehicleRecruitingPO.setApproveSchedule(0);
            int areaScope = enumRepository.getAreaScope(vehicleAddSOARequestType.getCityId());
            if (vehicleAddSOARequestType.getAreaScope() != null && vehicleAddSOARequestType.getAreaScope().intValue() != 1) {
                Result<Boolean> booleanResult = checkRegisterTime(vehicleAddSOARequestType);
                if(!booleanResult.isSuccess()){
                    Result.Builder.<Integer>newResult().fail().withCode(booleanResult.getCode()).withMsg(SharkUtils.getSharkValue(booleanResult.getMsg())).withData(-1).build();
                }

                if(vehicleAddSOARequestType.getVersionFlag()!=null){
                    //校验境内招募车辆是否否存在车牌号、VIN码
                    Result<Boolean> checkVehRecruitingResult  = drvVehRecruitingQueryService.checkRecruitingVehicleOnly(vehicleAddSOARequestType.getVehicleLicense(),vehicleAddSOARequestType.getVin());
                    if(!checkVehRecruitingResult.isSuccess()){
                        return Result.Builder.<Integer>newResult().fail().withMsg(checkVehRecruitingResult.getMsg()).withData(-1).build();
                    }
                    //是否存在vin码
                    if(vehicleRepository.checkVehOnly(vehicleAddSOARequestType.getVin(),TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())){
                        return Result.Builder.<Integer>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists)).withData(-1).build();
                    }
                }
            }
            vehicleRecruitingPO.setRegstDate(DateUtil.string2Timestamp(vehicleAddSOARequestType.getRegstDate(), DateUtil.YYYYMMDD));
            vehicleRecruitingPO.setApproveTime(DateUtil.getNow());
            vehicleRecruitingPO.setCertificateConfig(vehicleAddSOARequestType.getCertificateConfigStr());
            //工作台添加车辆,进车辆审批表
//        Long vehicleId = vehicleRepository.addVehicle(vehVehiclePO);
            Map<String,String> modSnapshotMap = Maps.newHashMap();
            modSnapshotMap.put("vehicleLicense",vehicleRecruitingPO.getVehicleLicense());
            modSnapshotMap.put("vin",vehicleRecruitingPO.getVin());
            vehicleRecruitingPO.setModSnapshotValues(JsonUtil.toJson(modSnapshotMap));
            //存储境外OCR校验结果
            if(CollectionUtils.isNotEmpty(vehicleAddSOARequestType.getOcrPassStatusList())){
                vehicleRecruitingPO.setOcrPassStatusJson(JsonUtil.toJson(vehicleAddSOARequestType.getOcrPassStatusList()));
            }

            Long vehicleRecruitingId = vehicleRecruitingRepository.addVehicleRecruiting(vehicleRecruitingPO);
            if (vehicleRecruitingId > 0) {
                //生成新版单项
                if(vehicleAddSOARequestType.getVersionFlag()!=null && vehicleAddSOARequestType.getVersionFlag()>=3 && areaScope == AreaScopeTypeEnum.DOMESTIC.getCode()){
                    recruitingCommandService.initSingleApprovalData(vehicleRecruitingId,TmsTransportConstant.SingleApproveTypeEnum.VEH.getCode(),vehicleAddSOARequestType.getModifyUser(),vehicleAddSOARequestType,null,vehicleAddSOARequestType.getChildCheckStatusList(), Maps.newHashMap());
                }
                modRecordRespository.insetModRecord(vehicleRecruitingId, null, CommonEnum.RecordTypeEnum.VEHRECRUIT, changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap(), vehicleAddSOARequestType.getModifyUser());
                BeanUtils.copyProperties(vehicleRecruitingPO, vehVehiclePO);
                vehVehiclePO.setVehicleId(vehicleRecruitingId);
                //添加招募司机，直接通过
                if(vehicleAddSOARequestType.getAction()!=null){
                    recruitingCommandService.approveRoute(buildRequest(vehicleAddSOARequestType,vehicleRecruitingId), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode());
                }
                if(vehicleAddSOARequestType.getVersionFlag() == null || vehicleAddSOARequestType.getVersionFlag() < 5){
                    //运营角色，自动通过审核
                    recruitingCommandService.recruitingApproveAdd(vehicleAddSOARequestType.getRoleCode(),enumRepository.getAreaScope(vehicleAddSOARequestType.getCityId()),TmsTransportConstant.VehicleFromEnum.Veh_MANUAL.getCode(),vehicleRecruitingId,vehicleAddSOARequestType.getCreateUser(),vehicleAddSOARequestType.getRemark(),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode());
                }
                updateCompliance(vehicleRecruitingId, vehicleAddSOARequestType);

                //境外新逻辑
                Result<Boolean> overseasResult =  recruitingCommandService.overseasNewBusinessApprove(vehicleAddSOARequestType.getSupplierId(),vehicleAddSOARequestType.getVersionFlag(),areaScope,vehicleRecruitingId,vehicleAddSOARequestType.getOcrPassStatusList(), TmsTransportConstant.RecruitingTypeEnum.vehicle, TmsTransportConstant.SingleApproveTypeEnum.VEH,vehicleAddSOARequestType.getModifyUser());
                if(!overseasResult.isSuccess()){
                    return Result.Builder.<Integer>newResult().fail().withCode(overseasResult.getCode()).withMsg(overseasResult.getMsg()).build();
                }

                //发送审批时效qmq
                qmqProducerCommandService.sendRecruitingApproveAgingQMQ(vehicleRecruitingId,TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode());
               return Result.Builder.<Integer>newResult().success().withData(vehicleRecruitingId.intValue()).build();
            }
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Integer>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgAddError))
                .withData(-1)
                .build();
    }

    private void updateCompliance(long vehicleId, VehicleAddSOARequestType requestType) {
        logger.info("updateCompliance start, vehicleId: {}, cityId: {}, vehicleLicense: {}",
                vehicleId, requestType.getCityId(), requestType.getVehicleLicense());

        Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "updateCompliance_vehicle_add_start");

        try {
            OcrComplianceDTO complianceRuleGary = internationalEntryService.isInComplianceRuleGary(requestType.getCityId());
            logger.info("updateCompliance query compliance rule, vehicleId: {}, cityId: {}, hasRule: {}",
                    vehicleId, requestType.getCityId(), Objects.nonNull(complianceRuleGary));

            if (Objects.nonNull(complianceRuleGary)) {
                logger.info("updateCompliance performing compliance check, vehicleId: {}, complianceType: {}",
                        vehicleId, complianceRuleGary.getComplianceType());

                ComplianceCheckResultDTO complianceResult = internationalEntryService.autoComplianceCheck(complianceRuleGary.getComplianceType(), requestType.getVehicleLicense(), requestType.getNewOcrFieldValue());

                Integer auditStatus = Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1;
                String reason = Objects.nonNull(complianceResult) ? complianceResult.getReason() : "";

                logger.info("updateCompliance check result, vehicleId: {}, auditStatus: {}, reason: {}",
                        vehicleId, auditStatus, reason);

                VehicleRecruitingPO vehPO = new VehicleRecruitingPO();
                vehPO.setVehicleId(vehicleId);
                vehPO.setAuditStatus(auditStatus);
                vehicleRecruitingRepository.update(vehPO);

                OcrPassStatusModelSOA ocrPassStatusModelSOA = new OcrPassStatusModelSOA();
                ocrPassStatusModelSOA.setOcrId(0L);
                ocrPassStatusModelSOA.setOcrItem(TmsTransportConstant.ApproveItemEnum.vehicle_compliance.getCode());
                ocrPassStatusModelSOA.setPassStatus(BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(auditStatus)));
                requestType.getOcrPassStatusList().add(ocrPassStatusModelSOA);

                logger.info("updateCompliance updated vehicle recruiting, vehicleId: {}, auditStatus: {}",
                        vehicleId, auditStatus);

                TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
                checkPO.setCheckId(vehicleId);
                checkPO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
                checkPO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.MANUAL_VEHICLE_AUDITSTATUS.getCode());
                checkPO.setCheckContent(reason);
                checkPO.setCheckStatus(VehicleAuditStatusEnum.getEnumByCode(auditStatus).getSingleApproveStatus());
                checkPO.setCreateUser(SessionHolder.getRestSessionAccountName());
                checkPO.setModifyUser(SessionHolder.getRestSessionAccountName());
                checkPO.setThirdCheckStatus(VehicleAuditStatusEnum.getEnumByCode(auditStatus).getSingleApproveStatus());
                checkRepository.insertTmsCertificateCheck(checkPO);

                logger.info("updateCompliance inserted certificate check, vehicleId: {}, checkStatus: {}",
                        vehicleId, checkPO.getCheckStatus());

                Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "updateCompliance_vehicle_add_success auditStatus:" + auditStatus);
            } else {
                logger.info("updateCompliance no compliance rule found, vehicleId: {}, cityId: {}",
                        vehicleId, requestType.getCityId());
                Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "updateCompliance_vehicle_add_no_rule");
            }

            logger.info("updateCompliance completed successfully, vehicleId: {}", vehicleId);
        } catch (Exception e) {
            logger.error("updateCompliance error, vehicleId: {}, cityId: {}", vehicleId, requestType.getCityId(), e);
            Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "updateCompliance_vehicle_add_error");
        }
    }



    protected Result<Boolean> checkRegisterTime(VehicleAddSOARequestType requestType) {
        String regstDate = requestType.getRegstDate();
        Long cityId = requestType.getCityId();
        if (BooleanUtils.isFalse(commonConfig.getOverageGraySwitch())  || (BooleanUtils.isTrue(commonConfig.getOverageGraySwitch()) && CollectionUtils.isNotEmpty(commonConfig.getCityIdList()) && commonConfig.getCityIdList().contains(cityId))) {
            Long vehicleTypeId = requestType.getVehicleTypeId();
            City city = enumRepository.getCityById(cityId);
            if (city.isChineseMainland()) {
                LocalDate now = LocalDate.now();
                LocalDate registerDate = now;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(regstDate)) {
                    registerDate = DateUtil.convertStringToDate(regstDate, DateUtil.YYYYMMDD);
                }
                if (registerDate.isAfter(now)) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
                }
                OverageDTO overageMap = overageQConfig.getOverageMap(cityId, vehicleTypeId);
                Double accessLimit = overageMap.getAccessLimit();
                BigDecimal accessMonth = BigDecimal.valueOf(accessLimit).multiply(BigDecimal.valueOf(12L));
                LocalDate overAgeDate = now.minusMonths(accessMonth.intValue());
                logger.info("checkRegisterTime", " licene :{}registerDate: {} overAgeDate :{}", requestType.getVehicleLicense(), registerDate.toString(), overAgeDate.toString());
                if (registerDate.isBefore(overAgeDate)) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();
                }
            }
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    private RecruitingApproveSOARequestType buildRequest(VehicleAddSOARequestType requestType, Long vehicleId){
        RecruitingApproveSOARequestType approveSOARequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO requestDTO = new RecruitingApproveSOARequestDTO();
        requestDTO.setAction(requestType.getAction());
        requestDTO.setMediumIdList(Arrays.asList(vehicleId));
        approveSOARequestType.setData(requestDTO);
        return approveSOARequestType;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateVehicle(VehicleUpdateSOARequestType vehicleUpdateSOARequestType) {
        VehVehiclePO vehicleDetailDTO = vehicleRepository.queryByPk(vehicleUpdateSOARequestType.getVehicleId());
        if (vehicleDetailDTO == null) {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withData(false).withMsg("vehicle  not found:" + vehicleUpdateSOARequestType.getVehicleId())
                    .build();
        }
        //废弃数据不可操作编辑 http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
        if(!vehicleDetailDTO.getActive()){
            return Result.Builder.<Boolean>newResult().fail().withData(false).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).build();
        }

        //车牌号不允许编辑:
        if (!StringUtils.isEmpty(vehicleUpdateSOARequestType.getVehicleLicense()) && !Objects.equals(vehicleUpdateSOARequestType.getVehicleLicense(),vehicleDetailDTO.getVehicleLicense())) {
            return Result.Builder.<Boolean>newResult()
              .fail()
              .withData(false).withCode(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_VEHICLE_LICENSE_EDIT.getCode()).withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_VEHICLE_LICENSE_EDIT.getMessage(), "Vehicle license do not support edit"))
              .build();
        }

        try {
            VehVehiclePO vehVehiclePO = new VehVehiclePO();
            BeanUtils.copyProperties(vehicleUpdateSOARequestType, vehVehiclePO);
            vehVehiclePO.setCertificateConfig(vehicleUpdateSOARequestType.getCertificateConfigStr());
            if(CollectionUtils.isNotEmpty(vehicleUpdateSOARequestType.getProLineList())){
                vehVehiclePO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(vehicleUpdateSOARequestType.getProLineList()));
            }
            vehVehiclePO.setRegstDate(new java.sql.Date(DateUtil.stringToDate(vehicleUpdateSOARequestType.getRegstDate(), DateUtil.YYYYMMDD).getTime()));

            // 判断供应商，城市两个字段是否有变更,如果有变更，看一下供应商能否在这个城市服务，且产线也满足需求
            boolean supplierChange = !Objects.equals(vehVehiclePO.getSupplierId(), vehicleDetailDTO.getSupplierId());
            boolean cityChange = !Objects.equals(vehVehiclePO.getCityId(), vehicleDetailDTO.getCityId());
            boolean productLineChange = !Objects.equals(vehVehiclePO.getCategorySynthesizeCode(), vehicleDetailDTO.getCategorySynthesizeCode());

            if (supplierChange || cityChange || productLineChange) {
                List<CategorySOADTO> contractList = queryCategoryService.getContractList(vehVehiclePO.getSupplierId(),
                  Lists.newArrayList(vehVehiclePO.getCityId()));
                List<Integer> showProductionLineList =
                  productionLineUtil.getShowProductionLineList(vehVehiclePO.getCategorySynthesizeCode());
                if (CollectionUtils.isEmpty(contractList) || !new HashSet<>(
                  contractList.stream().map(CategorySOADTO::getId).collect(Collectors.toList())).containsAll(showProductionLineList)) {
                    logger.info(TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getMessage(), "{},{}", showProductionLineList, contractList);
                    return this.resultErrorInfo(ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getMessage(), "Supplier, city, category not match"),String.valueOf(vehVehiclePO.getVehicleId()),false);
                }
            }

            // 判断是否有灰度中的供应商是否新增了包车产线
            if( (productionLineUtil.isProductLineCodeNewAddDayCheck(vehicleDetailDTO.getCategorySynthesizeCode(),vehVehiclePO.getCategorySynthesizeCode()) || productionLineUtil.isOnlyDayProductLine(vehVehiclePO.getCategorySynthesizeCode()))
              && driverGuidProxy.getGrayControl(vehVehiclePO.getSupplierId())) {
                return this.resultErrorInfo(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getMessage(), "Can not new add day product line"),String.valueOf(vehVehiclePO.getVehicleId()),false);
            }

            //网约车运输证号 更新值为空 且 数据库已存在该值  则不允许清空该值
            if(StringUtils.isEmpty(vehVehiclePO.getVehicleNetCertNo()) && !StringUtils.isEmpty(vehicleDetailDTO.getVehicleNetCertNo())){
                return Result.Builder.<Boolean>newResult().fail()
                        .withData(false).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.vehicleNetCertNoNotEmpty))
                        .build();
            }
            boolean domesticFlag =  enumRepository.getAreaScope(vehicleDetailDTO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode().intValue();
            //临派车辆
            Boolean temporaryDispatchMarkFlag = Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(),vehicleDetailDTO.getTemporaryDispatchMark());
            List<OcrPassStatusModelSOA> modelSOAS = vehicleUpdateSOARequestType.getOcrPassStatusList();
            //修改产线，车辆必须下线 未解绑
            if(Objects.equals(vehicleDetailDTO.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode()) && vehVehiclePO.getCategorySynthesizeCode() != null && !Objects.equals(vehicleDetailDTO.getCategorySynthesizeCode(),vehVehiclePO.getCategorySynthesizeCode()) &&
                    (!Objects.equals(TmsTransportConstant.VehStatusEnum.OFFLINE.getCode(),vehicleDetailDTO.getVehicleStatus()) || vehicleDetailDTO.getHasDrv())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvWarning2)).build();
            }
            // 判断如果更换了相关的证件，那么就进行合规判断
            CheckComplianceDTO checkComplianceDTO = checkComplianceStatus(vehicleUpdateSOARequestType, vehVehiclePO, vehicleDetailDTO, temporaryDispatchMarkFlag);
            Boolean inComplianceRuleFlag = checkComplianceDTO.getInComplianceRuleFlag();
            Integer auditStatus = checkComplianceDTO.getAuditStatus();

            //境外车辆不进编辑审核 update 放开境外编辑审核，车牌号 车身图片 车辆颜色进编辑审核
            if(tmsTransportQconfig.getUpdateApproveSwitch()){
                //当某字段在审核过程中时，即该字段修改事件审核状态为【待审核】时，该字段不可进行编辑。（即字段在审批流程中时，不可进行编辑修改操作）
                if (approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(vehicleUpdateSOARequestType.getVehicleId(), vehVehiclePO, TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE,
                    domesticFlag, TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode(), modelSOAS, temporaryDispatchMarkFlag, inComplianceRuleFlag, auditStatus, vehVehiclePO.getCityId()))) {
                    return Result.Builder.<Boolean>newResult().fail().withData(false).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportApproveIng)).build();
                }

                // 审批流逻辑
                Result<Long> result = approveCommandService.insertApprove(AddApproveDTO.buildAddDTO(vehicleDetailDTO.getVehicleId(), vehicleDetailDTO.getSupplierId(), vehVehiclePO, vehVehiclePO.getVehicleLicense(),
                    TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE, domesticFlag?changeRecordAttributeNameQconfig.getVehicleRecordMap():changeRecordAttributeNameQconfig.getOverseaVehicleRecordMap(), vehVehiclePO.getModifyUser(), null, domesticFlag, modelSOAS, temporaryDispatchMarkFlag,
                    inComplianceRuleFlag, auditStatus, vehVehiclePO.getCityId()), TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate, vehVehiclePO.getCityId());
                if(result.isSuccess() && result.getData() > 0){
                    if(domesticFlag){
                        //异步调用审批流中证件核验信息
                        tmsQmqProducerCommandService.sendApproveCertificateCheckQmq(result.getData(),TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode(), null,null, JsonUtil.toJson(vehicleDetailDTO),JsonUtil.toJson(vehVehiclePO));
                        vehVehiclePO.setVehicleCertiImg(vehicleDetailDTO.getVehicleCertiImg());
                        if (netCardCheckUtil.isNetCardNoNeedCheck(vehVehiclePO.getCityId())) {
                            vehVehiclePO.setNetTansCtfctImg(vehicleDetailDTO.getNetTansCtfctImg());
                        }
                        vehVehiclePO.setRegstDate(vehicleDetailDTO.getRegstDate());
                    }else {
                        // 给出自动合规的原因
                        approveCopyOrgin(vehVehiclePO,vehicleDetailDTO,vehicleUpdateSOARequestType.getSupplierId(),modelSOAS,temporaryDispatchMarkFlag,checkComplianceDTO);
                    }
                }
            }
            //只判断境外车辆的 车牌号、车身颜色、车身照片 变更
            Boolean vehicleLicenseModFlag = Boolean.FALSE;
            Boolean vehicleColorIdModFlag = Boolean.FALSE;
            Boolean vehicleFullImgModFlag = Boolean.FALSE;
            if(!domesticFlag){
                vehicleLicenseModFlag = !StringUtils.isEmpty(vehicleUpdateSOARequestType.getVehicleLicense()) && !Objects.equals(vehicleUpdateSOARequestType.getVehicleLicense(),vehicleDetailDTO.getVehicleLicense());
                vehicleFullImgModFlag = !StringUtils.isEmpty(vehicleUpdateSOARequestType.getVehicleFullImg()) && !Objects.equals(vehicleUpdateSOARequestType.getVehicleFullImg(),vehicleDetailDTO.getVehicleFullImg());
                vehicleColorIdModFlag = vehicleUpdateSOARequestType.getVehicleColorId()!= null && !Objects.equals(vehicleUpdateSOARequestType.getVehicleColorId(),vehicleDetailDTO.getVehicleColorId());
            }
            drvDrvierRepository.updateDrvVehicleType(vehicleUpdateSOARequestType.getVehicleId(), vehicleUpdateSOARequestType.getVehicleTypeId());
//            modRecordRespository.insetModRecord(vehicleUpdateSOARequestType.getVehicleId(), vehVehiclePO, CommonEnum.RecordTypeEnum.VEHICLE, changeRecordAttributeNameQconfig.getVehicleRecordMap(),vehicleUpdateSOARequestType.getModifyUser());
            if(CollectionUtils.isNotEmpty(vehicleUpdateSOARequestType.getOcrPassStatusList())){
                vehVehiclePO.setOcrPassStatusJson(JsonUtil.toJson(vehicleUpdateSOARequestType.getOcrPassStatusList()));
            }

            int resultLine = vehicleRepository.updateVehicle(vehVehiclePO);
            Long drvId;
            if ((drvId = drvDrvierRepository.queryDriverByVehicleId(vehicleUpdateSOARequestType.getVehicleId())) != null) {
                tmsQmqProducerCommandService.sendVehicleModifyQmq(vehicleUpdateSOARequestType.getVehicleId(), drvId);
            }
            if (vehicleDetailDTO.getVehicleTypeId() != null && vehicleUpdateSOARequestType.getVehicleTypeId() != null) {
                if (vehicleDetailDTO.getVehicleTypeId().longValue() != vehicleUpdateSOARequestType.getVehicleTypeId().longValue()) {
                    tmsQmqProducerCommandService.sendVehicleTypeModifyQmq(vehicleUpdateSOARequestType.getVehicleId(), vehicleUpdateSOARequestType.getVehicleTypeId(), drvId);
                }
            }
            if (resultLine > 0) {
                //修改证件状态
                if(CollectionUtils.isNotEmpty(vehicleUpdateSOARequestType.getCheckStatusList())){
                    for(UpdateCertificateStatusSOADTO statusSOADTO : vehicleUpdateSOARequestType.getCheckStatusList()){
                        checkRepository.updateCheckStatus(statusSOADTO.getId(),statusSOADTO.getCheckStatus());
                    }
                }
                //车辆变更车型,需要将车辆绑定的司机所有关联的报名制运力组解绑
                if(!Objects.equals(vehicleDetailDTO.getVehicleTypeId(),vehicleUpdateSOARequestType.getVehicleTypeId()) &&
                        drvId!=null && drvId > 0){
                    transportGroupCommandService.unBoundTransport(Arrays.asList(drvId),vehicleUpdateSOARequestType.getModifyUser(),Boolean.FALSE);
                }
                //境外车辆-供应商和平台均可以编辑“车牌号、车身颜色、车身照片”三个字段，提交编辑后，清空原始标签；
                if(!domesticFlag){
                    List<Integer> certificateType = Lists.newArrayList();
                    if(vehicleLicenseModFlag){
                        certificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLELICENSE.getCode());
                        //同步司机车牌
                        syncOverseasDrvVehLic(drvId,vehicleUpdateSOARequestType.getVehicleLicense(),modelSOAS);
                    }
                    if(vehicleColorIdModFlag){
                        certificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getCode());
                    }
                    if(vehicleFullImgModFlag){
                        certificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLEFULLIMG.getCode());
                    }
                    //清空标签，将active = 0
                    if(CollectionUtils.isNotEmpty(certificateType)){
                        checkRepository.updateCertificateActive(vehicleDetailDTO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode(),certificateType,Boolean.FALSE,vehicleUpdateSOARequestType.getModifyUser());
                    }
                }
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
            return Result.Builder.<Boolean>newResult().fail().withData(false).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private CheckComplianceDTO checkComplianceStatus(VehicleUpdateSOARequestType vehicleUpdateSOARequestType, VehVehiclePO vehVehiclePO, VehVehiclePO vehicleDetailDTO, Boolean temporaryDispatchMarkFlag) {
        logger.info("checkComplianceStatus start, vehicleId: {}, cityId: {}, vehicleLicense: {}, temporaryDispatchMarkFlag: {}", 
                vehVehiclePO.getVehicleId(), vehVehiclePO.getCityId(), vehVehiclePO.getVehicleLicense(), temporaryDispatchMarkFlag);
        
        // 初始化默认结果
        CheckComplianceDTO result = new CheckComplianceDTO();
        result.setInComplianceRuleFlag(false);
        result.setAuditStatus(1);
        result.setEditFieldList(new ArrayList<>());
        
        try {
            // 临派车辆直接返回默认值
            if (BooleanUtils.isTrue(temporaryDispatchMarkFlag)) {
                logger.info("checkComplianceStatus skip check for temporary dispatch vehicle, vehicleId: {}", vehVehiclePO.getVehicleId());
                return result;
            }

            // 查询合规规则
            logger.info("checkComplianceStatus query compliance rule, vehicleId: {}, cityId: {}", 
                    vehVehiclePO.getVehicleId(), vehVehiclePO.getCityId());
            OcrComplianceDTO complianceRule = internationalEntryService.isInComplianceRuleGary(vehVehiclePO.getCityId());
            
            // 没有合规规则，使用请求中的审核状态
            if (Objects.isNull(complianceRule)) {
                logger.info("checkComplianceStatus no compliance rule found, vehicleId: {}, use request auditStatus: {}", 
                        vehVehiclePO.getVehicleId(), vehicleUpdateSOARequestType.getVehicleAuditStatus());
                vehVehiclePO.setAuditStatus(vehicleUpdateSOARequestType.getVehicleAuditStatus());
                return result;
            }

            logger.info("checkComplianceStatus found compliance rule, vehicleId: {}, complianceType: {}, editFieldList: {}", 
                    vehVehiclePO.getVehicleId(), complianceRule.getComplianceType(), complianceRule.getEditFieldList());
            
            // 检查编辑字段列表
            List<String> editFieldList = complianceRule.getEditFieldList();
            if (CollectionUtils.isEmpty(editFieldList)) {
                logger.info("checkComplianceStatus compliance rule has no edit fields, vehicleId: {}", vehVehiclePO.getVehicleId());
                return result;
            }

            // 检查字段变更
            List<String> changedFields = checkFieldChanges(editFieldList, vehVehiclePO, vehicleDetailDTO);
            if (changedFields.isEmpty()) {
                logger.info("checkComplianceStatus no field changes detected, vehicleId: {}", vehVehiclePO.getVehicleId());
                return result;
            }

            // 有字段变更，执行自动合规检查
            logger.info("checkComplianceStatus fields changed, vehicleId: {}, changedFields: {}", 
                    vehVehiclePO.getVehicleId(), changedFields);
            
            Integer auditStatus = performAutoComplianceCheck(complianceRule, vehVehiclePO);
            vehVehiclePO.setAuditStatus(auditStatus);
            
            logger.info("checkComplianceStatus auto compliance check completed, vehicleId: {}, auditStatus: {}", 
                    vehVehiclePO.getVehicleId(), auditStatus);
            
            // 更新结果
            result.setInComplianceRuleFlag(true);
            result.setAuditStatus(auditStatus);
            result.setEditFieldList(changedFields);
            
        } catch (Exception e) {
            logger.error("checkComplianceStatus error, vehicleId: {}, cityId: {}", 
                    vehVehiclePO.getVehicleId(), vehVehiclePO.getCityId(), e);
            Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "checkComplianceStatus_error");
        }
        
        logger.info("checkComplianceStatus completed, vehicleId: {}, inComplianceRuleFlag: {}, auditStatus: {}, editFieldList: {}", 
                vehVehiclePO.getVehicleId(), result.getInComplianceRuleFlag(), result.getAuditStatus(), result.getEditFieldList());
        
        return result;
    }

    /**
     * 检查字段变更，返回变更的字段列表
     */
    private List<String> checkFieldChanges(List<String> editFieldList, VehVehiclePO vehVehiclePO, VehVehiclePO vehicleDetailDTO) {
        List<String> changedFields = new ArrayList<>();
        
        for (String field : editFieldList) {
            boolean isChanged = false;
            
            switch (field) {
                case "vehicleCertiImg":
                    isChanged = !Objects.equals(vehVehiclePO.getVehicleCertiImg(), vehicleDetailDTO.getVehicleCertiImg());
                    if (isChanged) {
                        logger.info("checkFieldChanges vehicleCertiImg changed, vehicleId: {}, old: {}, new: {}", 
                                vehVehiclePO.getVehicleId(), 
                                StringUtils.isEmpty(vehicleDetailDTO.getVehicleCertiImg()) ? "empty" : "exists",
                                StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg()) ? "empty" : "exists");
                    }
                    break;
                    
                case "vehicleLicense":
                    isChanged = !Objects.equals(vehVehiclePO.getVehicleLicense(), vehicleDetailDTO.getVehicleLicense());
                    if (isChanged) {
                        logger.info("checkFieldChanges vehicleLicense changed, vehicleId: {}, old: {}, new: {}", 
                                vehVehiclePO.getVehicleId(), vehicleDetailDTO.getVehicleLicense(), vehVehiclePO.getVehicleLicense());
                    }
                    break;
                    
                case "complianceQualificationCertificates":
                    isChanged = !Objects.equals(vehVehiclePO.getComplianceQualificationCertificates(), vehicleDetailDTO.getComplianceQualificationCertificates());
                    if (isChanged) {
                        logger.info("checkFieldChanges complianceQualificationCertificates changed, vehicleId: {}, old: {}, new: {}", 
                                vehVehiclePO.getVehicleId(), 
                                StringUtils.isEmpty(vehicleDetailDTO.getComplianceQualificationCertificates()) ? "empty" : "exists",
                                StringUtils.isEmpty(vehVehiclePO.getComplianceQualificationCertificates()) ? "empty" : "exists");
                    }
                    break;
                    
                default:
                    logger.warn("checkFieldChanges unknown field: {}, vehicleId: {}", field, vehVehiclePO.getVehicleId());
                    break;
            }
            
            if (isChanged) {
                changedFields.add(field);
            }
        }
        
        return changedFields;
    }

    /**
     * 执行自动合规检查
     */
    private Integer performAutoComplianceCheck(OcrComplianceDTO complianceRule, VehVehiclePO vehVehiclePO) {
        try {
            logger.info("performAutoComplianceCheck start, vehicleId: {}, complianceType: {}, vehicleLicense: {}", 
                    vehVehiclePO.getVehicleId(), complianceRule.getComplianceType(), vehVehiclePO.getVehicleLicense());
            
            ComplianceCheckResultDTO complianceResult = internationalEntryService.autoComplianceCheck(
                    complianceRule.getComplianceType(), 
                    vehVehiclePO.getVehicleLicense(), 
                    vehVehiclePO.getNewOcrFieldValue()
            );
            
            Integer auditStatus = Objects.nonNull(complianceResult) ? complianceResult.getAuditStatus() : 1;
            String reason = Objects.nonNull(complianceResult) ? complianceResult.getReason() : "";

            // 正式车辆的编辑，将自动合规计入到check表中
            TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
            checkPO.setCheckId(vehVehiclePO.getVehicleId());
            checkPO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
            checkPO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.AUTO_VEHICLE_AUDITSTATUS.getCode());
            checkPO.setCheckContent(reason);
            checkPO.setCheckStatus(VehicleAuditStatusEnum.getEnumByCode(auditStatus).getSingleApproveStatus());
            checkPO.setCreateUser(SessionHolder.getRestSessionAccountName());
            checkPO.setModifyUser(SessionHolder.getRestSessionAccountName());
            checkPO.setThirdCheckStatus(VehicleAuditStatusEnum.getEnumByCode(auditStatus).getSingleApproveStatus());
            checkRepository.insertTmsCertificateCheck(checkPO);
            
            logger.info("performAutoComplianceCheck result, vehicleId: {}, auditStatus: {}, reason: {}", 
                    vehVehiclePO.getVehicleId(), auditStatus, reason);
            
            Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "performAutoComplianceCheck_success auditStatus:" + auditStatus);
            
            return auditStatus;
        } catch (Exception e) {
            logger.error("performAutoComplianceCheck error, vehicleId: {}, complianceType: {}", 
                    vehVehiclePO.getVehicleId(), complianceRule.getComplianceType(), e);
            Cat.logEvent(CatEventType.INTERNATIONAL_COMPLIANCE_CHECK, "performAutoComplianceCheck_error");
            return 1; // 默认返回通过状态
        }
    }



    protected Result<Boolean> resultErrorInfo(String code, String errorMsg, String infoBean, Boolean data) {
        return Result.Builder.<Boolean>newResult()
          .fail()
          .withCode(code)
          .withMsg(Strings.isNullOrEmpty(infoBean) ? errorMsg :"【" + infoBean + "】" + errorMsg)
          .withData(data)
          .build();
    }

    @Override
    public Result<Boolean> updateVehicleStatus(VehicleUpdateStatusSOARequestType requestType) {
        if(CollectionUtils.isEmpty(requestType.getVehicleIdList()) || requestType.getVehicleStatus() == null){
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withData(false).withMsg("parsms not found:")
                    .build();
        }
        try{
            int count = vehicleRepository.updateVehicleStatus(requestType.getVehicleIdList(),requestType.getVehicleStatus(),requestType.getModifyUser());
            if(count > 0){
                //车辆下线，车辆与已绑定的司机进行解绑，并且车辆状态变更为下线状态，是否绑定司机改为未绑定。
                // （后续触发，司机解绑车辆后，司机的待服务订单会通知派发侧进行改派操作）
                VehicleUpdateStatusDTO vehicleUpdateStatusDTO = new VehicleUpdateStatusDTO();
                vehicleUpdateStatusDTO.setVehicleIdList(requestType.getVehicleIdList());
                vehicleUpdateStatusDTO.setVehicleStatus(requestType.getVehicleStatus());
                vehicleUpdateStatusDTO.setModifyUser(requestType.getModifyUser());
                offlineVehicles(vehicleUpdateStatusDTO);
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportOperatingFail)).build();
    }

    public void offlineVehicles(VehicleUpdateStatusDTO requestType) {
        if(Objects.equals(TmsTransportConstant.VehStatusEnum.OFFLINE.getCode(), requestType.getVehicleStatus())){
            List<DrvDriverPO> drvDriverPOList =  drvDrvierRepository.queryDrvByVehicleIds(requestType.getVehicleIdList());
            List<Long> drvIds = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(drvDriverPOList)){
                drvDriverPOList.forEach(drvDriverPO -> drvIds.add(drvDriverPO.getDrvId()));
                //司机解绑车辆
                drvDrvierRepository.unbindCarforDrv(drvIds, requestType.getModifyUser());
                //司机下线解绑运力组
                transportGroupCommandService.unBoundTransport(drvIds, requestType.getModifyUser(),false);
                // 发送司机解绑车辆qmq
                for(Long drvId:drvIds){
                    tmsQmqProducerCommandService.sendDrvVehicleChangeQmq(drvId,Long.valueOf(0));
                }
            }
            //解绑工作台创建的招募司机已绑定的正式车辆
            recruitingRepository.unBingVehicleFromDrvRecruiting(requestType.getVehicleIdList(), requestType.getModifyUser());
        }
    }

    @Override
    public void updateVehicleAgeType(VehVehiclePO vehicle, String code, String system) {
        logger.info("updateVehicleAgeType", "{},{},{}", vehicle.getVehicleId(), code, system);
        vehicleRepository.updateVehicleAgeType(vehicle.getVehicleId(), code,system);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> temporaryDispatchVehicleAdd(TemporaryDispatchVehicleAddSOARequestType requestType) {
        try {
            //判断车牌号是否唯一
            Result<Boolean> uniqueness = checkUniqueness(requestType.getVehicleLicense());
            if(!uniqueness.isSuccess()){
                return Result.Builder.<Long>newResult().fail().withCode(uniqueness.getCode()).build();
            }
            //获取产线-供应商+城市下所有已上线的运力组的产线并集
            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryGroupBySupplierAndCity(Arrays.asList(requestType.getSupplierId()),Arrays.asList(requestType.getCityId()));
            if(CollectionUtils.isEmpty(transportGroupPOList)){
                return Result.Builder.<Long>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.capacitygroupTemporary)).build();
            }
            transportGroupPOList = supplierDayProductLineMigrationHelper.filterDayProductLineIfSupplier(requestType.getSupplierId(),transportGroupPOList);
            //运力组中的产线
            Set<Integer> proLineList = transportGroupPOList.stream().map(TspTransportGroupPO::getCategorySynthesizeCode).collect(Collectors.toSet());

            Timestamp dispatchTime = DateUtil.nowDatePlusHours(overseasQconfig.getTemporaryDispatchEndDatetimeConfig());
            VehVehiclePO vehVehiclePO = new VehVehiclePO();
            BeanUtils.copyProperties(requestType,vehVehiclePO);
            vehVehiclePO.setCountryId(enumRepository.getCountryId(requestType.getCityId()));
            vehVehiclePO.setCountryName(enumRepository.getCountryName(enumRepository.getCountryId(requestType.getCityId())));
            vehVehiclePO.setCategorySynthesizeCode(productionLineUtil.insideProLineMerge(proLineList));
            vehVehiclePO.setVehicleStatus(TmsTransportConstant.VehStatusEnum.ONLINE.getCode());
            vehVehiclePO.setCreateUser(SessionHolder.getRestSessionAccountName());
            vehVehiclePO.setModifyUser(SessionHolder.getRestSessionAccountName());
            vehVehiclePO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode());
            vehVehiclePO.setTemporaryDispatchEndDatetime(dispatchTime);
            Long vehicleId = vehicleRepository.addVehicle(vehVehiclePO);
            if(vehicleId > 0){
                tmsQmqProducerCommandService.sendTemporaryReplenishInfo(vehicleId,TmsTransportConstant.AutoDiscardTemporaryTypeEnum.VEH.getCode(),null);
            }
            return Result.Builder.<Long>newResult().success().withData(vehicleId).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
    　* @description: 车辆从临派置为正式
                     则将当车牌号对应的所有已废弃的并且是临派的车辆都置为正式废弃，和订单同步
    　* <AUTHOR>
    　* @date 2023/10/24 14:37
    */
    @Override
    public Boolean temToOfficialSendQmq(Long vehicleId, String vehicleLicense, String modifyUser) {
        try {
            //查询车牌号下所有废弃的临派车辆ID
            List<Long> vehicleIds = vehicleRepository.queryDiscardTemVeh(vehicleLicense);
            if(vehicleId == null && CollectionUtils.isEmpty(vehicleIds)){
                return Boolean.FALSE;
            }
            //将已废弃的临派车辆置为正式
            vehicleRepository.updateTemporaryDispatchMark(vehicleIds,Boolean.FALSE,modifyUser);
            List<Long> sendVehIds = Lists.newArrayList();
            if(vehicleId !=null ){
                sendVehIds.add(vehicleId);
            }
            if(CollectionUtils.isNotEmpty(vehicleIds)){
                sendVehIds.addAll(vehicleIds);
            }
            //发送临派转正式qmq给采购
            tmsQmqProducerCommandService.sendTemporaryToOfficialQmq(sendVehIds,TmsTransportConstant.AutoDiscardTemporaryTypeEnum.VEH.getCode());
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //手机号车牌号是否唯一
    public Result<Boolean> checkUniqueness(String vehicleLicense){
        try {
            //判断车牌号唯一
            List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryVehByVehicleLicense(vehicleLicense);
            if(CollectionUtils.isNotEmpty(vehVehiclePOList)){
                VehVehiclePO vehVehiclePO = vehVehiclePOList.get(0);
                if(!Objects.isNull(vehVehiclePO)){
                    //查询当前重复数据是正式数据还是临派数据
                    return CtripCommonUtils.resultVehUniquenessCode(vehVehiclePO.getTemporaryDispatchMark());
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //进编辑审批的字段，在审批之前值不变
    public Boolean approveCopyOrgin(VehVehiclePO vehVehiclePO, VehVehiclePO vehicleDetailDTO, Long supplierId, List<OcrPassStatusModelSOA> modelSOAS, Boolean temporaryDispatchMarkFlag, CheckComplianceDTO checkComplianceDTO){
        vehVehiclePO.setVehicleFullImg(vehicleDetailDTO.getVehicleFullImg());
        //兼容原数据，境外进编辑审批 update 新增临派车辆编辑，如果是临派车辆并且OCR为空，则会进入编辑审核，数据不变
        Boolean supplierGray = driverQueryService.overseasSupplierIsGray(supplierId, enumRepository.getAreaScope(vehicleDetailDTO.getCityId()));
        if(((!supplierGray || CollectionUtils.isEmpty(modelSOAS)) && !temporaryDispatchMarkFlag) ||
                (temporaryDispatchMarkFlag && CollectionUtils.isEmpty(modelSOAS))){
            vehVehiclePO.setVehicleColorId(vehicleDetailDTO.getVehicleColorId());
            vehVehiclePO.setVehicleLicense(vehicleDetailDTO.getVehicleLicense());
        }

        if (checkComplianceDTO.getInComplianceRuleFlag()) {
            List<String> editFieldList = checkComplianceDTO.getEditFieldList();
            if (CollectionUtils.isNotEmpty(editFieldList)) {
                for (String field : editFieldList) {
                    switch (field) {
                        case "vehicleCertiImg":
                            vehVehiclePO.setVehicleCertiImg(vehicleDetailDTO.getVehicleCertiImg());
                            break;
                        case "complianceQualificationCertificates":
                            vehVehiclePO.setComplianceQualificationCertificates(vehicleDetailDTO.getComplianceQualificationCertificates());
                            break;
                        case "vehicleLicense":
                            vehVehiclePO.setVehicleLicense(vehicleDetailDTO.getVehicleLicense());
                            break;
                        default:
                            break;
                    }
                }
            }
            vehVehiclePO.setAuditStatus(vehicleDetailDTO.getAuditStatus());
        }

        //新逻辑，如果OCR校验值为空，则表示没有变更值
        if(CollectionUtils.isNotEmpty(modelSOAS)){
            for(OcrPassStatusModelSOA soa : modelSOAS){
                if(Objects.equals(soa.getPassStatus(), TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass.getCode())){
                    if(Objects.equals(soa.getOcrItem(),TmsTransportConstant.ApproveItemEnum.vehicle_color.getCode())){
                        vehVehiclePO.setVehicleColorId(vehicleDetailDTO.getVehicleColorId());
                    }
                    if(Objects.equals(soa.getOcrItem(),TmsTransportConstant.ApproveItemEnum.vehicle_number.getCode())){
                        vehVehiclePO.setVehicleLicense(vehicleDetailDTO.getVehicleLicense());
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    //同步司机车牌信息
    public Boolean syncOverseasDrvVehLic(Long drvId,String vehicleLicense,List<OcrPassStatusModelSOA> modelSOAS){
        if(drvId == null || drvId == 0 || CollectionUtils.isEmpty(modelSOAS)){
            return Boolean.FALSE;
        }

        try {
            Boolean syncFlag = Boolean.FALSE;
            //如果OCR校验车牌结果为通过，则直接修改车牌成功，不进编辑审批
            for(OcrPassStatusModelSOA soa : modelSOAS){
                if(Objects.equals(soa.getPassStatus(), TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode())){
                    if(Objects.equals(soa.getOcrItem(),TmsTransportConstant.ApproveItemEnum.vehicle_number.getCode())){
                        syncFlag = Boolean.TRUE;
                        break;
                    }
                }
            }

            if(!syncFlag){
                return Boolean.FALSE;
            }
            //同步司机车牌
            drvDrvierRepository.syncDrvVehicleLicense(drvId,vehicleLicense, SessionHolder.getRestSessionAccountName());
            //司机信息变更，发送变更消息
            tmsQmqProducerCommandService.sendDrvChangeQmq(drvId, 2,1);
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("syncOverseasDrvVehLicError ","drvId:{}",drvId,e);
            return Boolean.FALSE;
        }
    }

    @Override
    public Result<Long> generateGlobalId(String vehicleLicense, String source) {
        return vehicleGlobalIdRecordRepository.generateGlobalId(vehicleLicense, source);
    }
}
