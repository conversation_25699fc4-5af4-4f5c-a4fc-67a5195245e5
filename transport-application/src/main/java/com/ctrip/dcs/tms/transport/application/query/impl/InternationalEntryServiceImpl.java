package com.ctrip.dcs.tms.transport.application.query.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.basebiz.ai.aiplatform.contract.api.AiPlatformServiceClient;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformRequest;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformResponse;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ComplianceStrategyEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.InternationalEntryConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.NewOcrFiledConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalSnowFlakeUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OverseasOcrRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OverseasOcrRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

@Service
public class InternationalEntryServiceImpl implements InternationalEntryService {
    private Logger logger = LoggerFactory.getLogger(InternationalEntryServiceImpl.class);
    @Autowired
    private InternationalEntryConfig internationalEntryConfig;

    @Autowired
    private NewOcrFiledConfig newOcrFiledConfig;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    @Qualifier("aiPlatformServiceClient")
    private AiPlatformServiceClient aiPlatformServiceClient;

    @Autowired
    private OverseasOcrRecordRepository overseasOcrRecordRepository;

    private LocalSnowFlakeUtil localSnowFlakeUtil = new LocalSnowFlakeUtil();


    private Integer mapImgTypeToRequestType(String imgType) {
        return CommonEnum.ImageTypeEnum.getCode(imgType);
    }

    /**
     * 从数据库记录中解析OCR结果
     * @param record 数据库记录
     * @param imgType 图片类型
     * @return OCR结果列表
     */
    private List<OcrResultDTO> parseOcrResultFromRecord(OverseasOcrRecordPO record, String imgType) {
        List<OcrResultDTO> resultList = new ArrayList<>();

        try {
            String responseResult = record.getResponseResult();
            if (StringUtils.isBlank(responseResult)) {
                return resultList;
            }

            // 这里需要根据实际存储的格式来解析
            // 假设存储的是JSON格式的OCR结果
            Map<String, String> resultMap = JsonUtil.fromJson(responseResult, new TypeReference<Map<String, String>>() {});

            // 获取配置信息以确定需要返回哪些字段
            OcrRecognitionResultDTO recognitionResultDTO = newOcrFiledConfig.getOcrRecognitionResultByCityWithFallback(record.getRequestCityId(), imgType);
            if (recognitionResultDTO != null && CollectionUtils.isNotEmpty(recognitionResultDTO.getResponse())) {
                for (OcrRespDTO ocrRespDTO : recognitionResultDTO.getResponse()) {
                    String field = ocrRespDTO.getField();
                    String value = resultMap.get(field);

                    OcrResultDTO ocrResultDTO = new OcrResultDTO();
                    ocrResultDTO.setFieldName(field);
                    ocrResultDTO.setBackFillField(ocrRespDTO.getBackFillField());
                    ocrResultDTO.setValue(value);
                    ocrResultDTO.setBackfill(ocrRespDTO.getBackfill());
                    ocrResultDTO.setOcrId(record.getId());
                    resultList.add(ocrResultDTO);
                }
            }
        } catch (Exception e) {
            logger.error("parseOcrResultFromRecord","解析OCR记录结果时发生异常，recordId: {}", record.getId(), e);
        }

        return resultList;
    }

    /**
     * 是否在安全合规的范围内
     *
     * @param cityId 城市ID
     * @param supplierId 供应商id
     * @return {@link Result }<{@link Boolean }>
     */
    @Override
    public Result<Boolean> isInComplianceRuleGary(Long cityId, Long supplierId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.isInComplianceRuleGary");
        String methodName = "isInComplianceRuleGary";
        Result<Boolean> result = null;
        
        try {
            // 记录入参日志
            logger.info("isInComplianceRuleGary","{} 方法开始执行，入参: cityId={}, supplierId={}", methodName, cityId, supplierId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            List<Long> complianceGaryCountry = internationalEntryConfig.getComplianceGaryCountry();
            List<Long> complianceGaryCity = internationalEntryConfig.getComplianceGaryCity();

            if (complianceGaryCity.contains(cityId)) {
                result = Result.Builder.<Boolean>newResult().success().withData(true).build();
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            Long countryId = enumRepository.getCountryId(cityId);
            if (complianceGaryCountry.contains(countryId)) {
                result = Result.Builder.<Boolean>newResult().success().withData(true).build();
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            result = Result.Builder.<Boolean>newResult().success().withData(false).build();
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("isInComplianceRuleGary","{} 方法执行异常，入参: cityId={}, supplierId={}", methodName, cityId, supplierId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("isInComplianceRuleGary","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    /**
     * 查询所需字段列表
     *
     * @param cityId     城市ID
     * @param supplierId 供应商id
     * @param sceneList
     * @return {@link Result }<{@link List }<{@link String }>>
     */
    @Override
    public Result<List<RequiredFieldDTO>> queryRequiredFiledList(Long cityId, Long supplierId, List<String> sceneList) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.queryRequiredFiledList");
        String methodName = "queryRequiredFiledList";
        Result<List<RequiredFieldDTO>> result = null;
        
        try {
            // 记录入参日志
            logger.info("queryRequiredFiledList","{} 方法开始执行，入参: cityId={}, supplierId={}, sceneList={}", methodName, cityId, supplierId, JsonUtil.toJson(sceneList));
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            List<RequiredFieldDTO> resultList = newOcrFiledConfig.getFieldListByCityWithFallback(cityId, sceneList);

            if (CollectionUtils.isNotEmpty(resultList)) {
                result = Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(resultList).build();
            } else {
                result = Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(null).build();
            }
            
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("queryRequiredFiledList","{} 方法执行异常，入参: cityId={}, supplierId={}, sceneList={}", methodName, cityId, supplierId, JsonUtil.toJson(sceneList), e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("queryRequiredFiledList","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    @Override
    public Result<OcrRecoResultDTO> ocrRecognition(OcrReqDTO ocrReqDTO) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.ocrRecognition");
        String methodName = "ocrRecognition";
        Result<OcrRecoResultDTO> result = null;

        try {
            // 记录入参日志
            logger.info("ocrRecognition","{} 方法开始执行，入参: {}", methodName, JsonUtil.toJson(ocrReqDTO));
            Cat.logEvent("InternationalEntry.Method.Start", methodName);

            // 1. 参数预处理和验证
            OcrProcessContext context = buildOcrProcessContext(ocrReqDTO);
            if (context == null) {
                return buildFailResult("参数验证失败", "INVALID_PARAMETERS");
            }

            // 2. 尝试从缓存获取结果
            Result<OcrRecoResultDTO> cachedResult = tryGetCachedOcrResult(context);
            if (cachedResult != null) {
                transaction.setStatus(Transaction.SUCCESS);
                return cachedResult;
            }

            // 3. 执行OCR识别
            result = performOcrRecognition(context);
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("ocrRecognition","{} 方法执行异常，入参: {}", methodName, JsonUtil.toJson(ocrReqDTO), e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("ocrRecognition","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    /**
     * 合规性验证 返回合规状态和具体原因
     * 场景  ：新增招募、更新招募信息、新增车辆、更新车辆
     * @param inComplianceRule 合规规则
     * @param vehicleLicense   车辆牌照
     * @param newOcrFieldValue 新ocr字段值
     * @return ComplianceVerificationResultDTO 包含合规状态和不合规原因
     */
    @Override
    public ComplianceCheckResultDTO autoComplianceCheck(Integer inComplianceRule, String vehicleLicense, String newOcrFieldValue) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.complianceVerification");
        String methodName = "complianceVerification";
        ComplianceCheckResultDTO result = null;

        try {
            // 记录入参日志
            logger.info("complianceVerification","{} 方法开始执行，入参: inComplianceRule={}, vehicleLicense={}, newOcrFieldValue={}",
                       methodName, inComplianceRule, vehicleLicense, newOcrFieldValue);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);

            if (ComplianceStrategyEnum.FIRST_STRATEGY.getCode().equals(inComplianceRule)) {
                result = japanCompliance(vehicleLicense, newOcrFieldValue);
            }else if (ComplianceStrategyEnum.SECOND_STRATEGY.getCode().equals(inComplianceRule)) {
                result = singaporeCompliance(vehicleLicense, newOcrFieldValue);
            }else if (ComplianceStrategyEnum.THIRD_STRATEGY.getCode().equals(inComplianceRule)) {
                result = koreaCompliance(vehicleLicense);
            } else {
                result = ComplianceCheckResultDTO.manualReview("未知的合规规则: " + inComplianceRule);
            }

            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("complianceVerification","{} 方法执行异常，入参: inComplianceRule={}, vehicleLicense={}, newOcrFieldValue={}",
                       methodName, inComplianceRule, vehicleLicense, newOcrFieldValue, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("complianceVerification","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    private ComplianceCheckResultDTO koreaCompliance(String vehicleLicense) {
        String methodName = "koreaCompliance";
        logger.info("koreaCompliance","{} 方法开始执行，入参: vehicleLicense={}", methodName, vehicleLicense);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceCheckResultDTO result = null;
        try {
            AtomicReference<Boolean> koreaVehivleApprove = new AtomicReference<>(false);
            if (StringUtils.isBlank(vehicleLicense)) {
                result = ComplianceCheckResultDTO.manualReview("车辆牌照为空");
            }

            List<String> approveVehicleList = internationalEntryConfig.getKoreaApproveVehicleList();
            if (CollectionUtils.isNotEmpty(approveVehicleList)) {
                koreaVehivleApprove.set(approveVehicleList.stream().anyMatch(approveVehicle -> vehicleLicense.contains(approveVehicle)));
            }
            if (BooleanUtils.isTrue(koreaVehivleApprove.get())) {
                result = ComplianceCheckResultDTO.compliant();
            } else {
                result = ComplianceCheckResultDTO.manualReview("韩国车牌号码不包含批准的字符");
            }

            return result;
        } finally {
            if (result != null) {
                logger.info("koreaCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }

    private ComplianceCheckResultDTO singaporeCompliance(String vehicleLicense, String newOcrFieldValue) {
        String methodName = "singaporeCompliance";
        logger.info("singaporeCompliance","{} 方法开始执行，入参: vehicleLicense={}, newOcrFieldValue={}", methodName, vehicleLicense, newOcrFieldValue);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceCheckResultDTO result = null;
        try {
            if (StringUtils.isBlank(vehicleLicense)) {
                result = ComplianceCheckResultDTO.nonCompliant("车辆牌照为空");
                return result;
            }
            if (StringUtils.isBlank(newOcrFieldValue)) {
                result = ComplianceCheckResultDTO.nonCompliant("新ocr字段值为空");
            }
            Map<String, List<Map<String, String>>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, List<Map<String, String>>>>() {});
            List<Map<String, String>> stringStringMap2 = stringStringMap.get("complianceQualificationCertificates");
            if (CollectionUtils.isEmpty(stringStringMap2)) {
                result = ComplianceCheckResultDTO.nonCompliant("合规资质证书信息为空");
                return result;
            }
            String vehicleNature =  getStr(stringStringMap2, "VehicleNature");
            String licensePlateNo =  getStr(stringStringMap2, "LicensePlateNo");
            String vehicleScheme =  getStr(stringStringMap2, "VehicleScheme");

            boolean startsWithP = vehicleLicense.startsWith("P");
            boolean startsWithSH = vehicleLicense.startsWith("SH");
            boolean privateHire = vehicleNature != null && vehicleNature.replace("\\s+", "").toLowerCase().contains("privatehire");
            boolean publicServiceVehicle = vehicleScheme != null && vehicleScheme.replace("\\s+", "").toLowerCase().contains("publicservicevehicle");
            boolean equals = StringUtils.equals(licensePlateNo, vehicleLicense);

            if (startsWithSH) {
                result = ComplianceCheckResultDTO.compliant();
                return result;
            }
            if (startsWithP) {
                if (publicServiceVehicle && equals) {
                    result = ComplianceCheckResultDTO.compliant();
                    return result;
                }
                if (!equals && !publicServiceVehicle) {
                    result = ComplianceCheckResultDTO.nonCompliant("P开头车牌号码不匹配且不是公共服务车辆");
                    return result;
                }
                result = ComplianceCheckResultDTO.manualReview("P开头车牌需要人工审核");
                return result;
            }
            if (privateHire) {
                result = ComplianceCheckResultDTO.compliant();
                return result;
            }
            result = ComplianceCheckResultDTO.manualReview("车辆性质不是私人租赁，需要人工审核");
            return result;
        } finally {
            if (result != null) {
                logger.info("singaporeCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }

    private String getStr(List<Map<String, String>> stringStringMap2, String vehicleNature) {

        if (CollectionUtils.isNotEmpty(stringStringMap2)) {
            for (Map<String, String> stringStringMap : stringStringMap2) {
                String s = stringStringMap.get("fieldName");
                if (StringUtils.equals(s, vehicleNature)) {
                    return stringStringMap.get("value");
                }
            }
        }
        return null;
    }


    @Nullable
    private ComplianceCheckResultDTO japanCompliance(String vehicleLicense, String newOcrFieldValue) {
        String methodName = "japanCompliance";
        logger.info("japanCompliance","{} 方法开始执行，入参: vehicleLicense={}, newOcrFieldValue={}", methodName, vehicleLicense, newOcrFieldValue);
        Cat.logEvent("InternationalEntry.Method.Start", methodName);

        ComplianceCheckResultDTO result = null;
        try {
            if (StringUtils.isNotBlank(newOcrFieldValue)) {
                Map<String, List<Map<String, String>>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, List<Map<String, String>>>>() {});
                List<Map<String, String>> stringStringMap1 = stringStringMap.get("vehicleCertiImg");
                if (stringStringMap1 == null) {
                    result = ComplianceCheckResultDTO.manualReview("车辆证书图片信息为空");
                    return result;
                }
                String useType =  getStr(stringStringMap1, "UseType");
                Boolean useAge = false;
                AtomicReference<Boolean> vehivleApprove = new AtomicReference<>(false);

                if (StringUtils.isNotBlank(useType)) {
                    List<String> japanApproveStrList = internationalEntryConfig.getJapanApproveStrList();
                    if (CollectionUtils.isNotEmpty(japanApproveStrList)) {
                        useAge = japanApproveStrList.stream().anyMatch(useType::contains);
                    }
                }
                if (StringUtils.isNotBlank(vehicleLicense)) {
                    List<String> approveVehicleList = internationalEntryConfig.getJapanApproveVehicleList();
                    if (CollectionUtils.isNotEmpty(approveVehicleList)) {
                        vehivleApprove.set(approveVehicleList.stream().anyMatch(vehicleLicense::contains));
                    }
                }
                if (BooleanUtils.isTrue(useAge) && BooleanUtils.isTrue(vehivleApprove.get())) {
                    result = ComplianceCheckResultDTO.compliant();
                    return result;
                }
                if (BooleanUtils.isFalse(useAge) && BooleanUtils.isFalse(vehivleApprove.get())) {
                    result = ComplianceCheckResultDTO.nonCompliant("车辆用途不是事业用且车牌号码不包含批准的字符");
                    return result;
                }
                result = ComplianceCheckResultDTO.manualReview("车辆用途或车牌号码部分符合要求，需要人工审核");
                return result;
            }
            result = ComplianceCheckResultDTO.manualReview("OCR字段值为空，需要人工审核");
            return result;
        } finally {
            if (result != null) {
                logger.info("japanCompliance","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
        }
    }

    /**
     * 自动合规判断范围以及判断字段
     *
     * @param cityId 城市ID
     * @return {@link OcrComplianceDTO }
     */
    @Override
    public OcrComplianceDTO isInComplianceRuleGary(Long cityId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.isInComplianceRuleGary_overload");
        String methodName = "isInComplianceRuleGary(cityId)";
        OcrComplianceDTO result = null;
        
        try {
            // 记录入参日志
            logger.info("isInComplianceRuleGary","{} 方法开始执行，入参: cityId={}", methodName, cityId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            result = newOcrFiledConfig.getOcrComplianceByCityWithFallback(cityId);
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("isInComplianceRuleGary","{} 方法执行异常，入参: cityId={}", methodName, cityId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            logger.info("isInComplianceRuleGary","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
            Cat.logEvent("InternationalEntry.Method.End", methodName);
            transaction.complete();
        }
    }

    @Override
    public InternatSettleOCRDTO useNewOCR(Long cityId, Long supplierId) {
        Transaction transaction = Cat.newTransaction("Service", "InternationalEntryService.useNewOCR");
        String methodName = "useNewOCR";
        InternatSettleOCRDTO result = null;
        
        try {
            // 记录入参日志
            logger.info("useNewOCR","{} 方法开始执行，入参: cityId={}, supplierId={}", methodName, cityId, supplierId);
            Cat.logEvent("InternationalEntry.Method.Start", methodName);
            
            InternatSettleOCRDTO internatSettleOCRDTO = new InternatSettleOCRDTO();
            NewOCRDTO newOcr = newOcrFiledConfig.getNewOcrByCityWithFallback(cityId);
            if (Objects.nonNull(newOcr)) {
                internatSettleOCRDTO.setUseNewOCR(true);
                internatSettleOCRDTO.setNewOCRFieldList(newOcr.getOcrFieldList());
                result = internatSettleOCRDTO;
                transaction.setStatus(Transaction.SUCCESS);
                return result;
            }
            internatSettleOCRDTO.setUseNewOCR(false);
            internatSettleOCRDTO.setNewOCRFieldList(new ArrayList<>());
            result = internatSettleOCRDTO;
            transaction.setStatus(Transaction.SUCCESS);
            return result;
        } catch (Exception e) {
            logger.error("useNewOCR","{} 方法执行异常，入参: cityId={}, supplierId={}", methodName, cityId, supplierId, e);
            Cat.logError(e);
            transaction.setStatus(e);
            throw e;
        } finally {
            if (result != null) {
                logger.info("useNewOCR","{} 方法执行完成，出参: {}", methodName, JsonUtil.toJson(result));
                Cat.logEvent("InternationalEntry.Method.End", methodName);
            }
            transaction.complete();
        }
    }

    // ========== OCR识别优化后的辅助方法 ==========

    /**
     * OCR处理上下文
     */
    private static class OcrProcessContext {
        private final Long cityId;
        private final String imgType;
        private final String originalUrl;
        private final String processedUrl;
        private final Integer requestType;
        private final OcrRecognitionResultDTO recognitionConfig;

        public OcrProcessContext(Long cityId, String imgType, String originalUrl, String processedUrl,
                               Integer requestType, OcrRecognitionResultDTO recognitionConfig) {
            this.cityId = cityId;
            this.imgType = imgType;
            this.originalUrl = originalUrl;
            this.processedUrl = processedUrl;
            this.requestType = requestType;
            this.recognitionConfig = recognitionConfig;
        }

        // Getters
        public Long getCityId() { return cityId; }
        public String getImgType() { return imgType; }
        public String getOriginalUrl() { return originalUrl; }
        public String getProcessedUrl() { return processedUrl; }
        public Integer getRequestType() { return requestType; }
        public OcrRecognitionResultDTO getRecognitionConfig() { return recognitionConfig; }
    }

    /**
     * 构建OCR处理上下文
     */
    private OcrProcessContext buildOcrProcessContext(OcrReqDTO ocrReqDTO) {
        try {
            Long cityId = ocrReqDTO.getCityId();
            String imgType = ocrReqDTO.getImgType();
            String imgUrl = ocrReqDTO.getImgUrl();

            // 参数验证
            if (cityId == null || StringUtils.isBlank(imgType) || StringUtils.isBlank(imgUrl)) {
                logger.warn("buildOcrProcessContext","OCR请求参数不完整，cityId: {}, imgType: {}, imgUrl: {}",
                           cityId, imgType, imgUrl);
                return null;
            }

            // URL处理
            String newDomain = internationalEntryConfig.getImageUrlPrefix();
            String processedUrl = imgUrl.replaceFirst(internationalEntryConfig.getImageReplaceRegular(), newDomain)
                                        .replace("https://", "http://");

            // 映射imgType到requestType
            Integer requestType = mapImgTypeToRequestType(imgType);

            // 获取OCR配置
            OcrRecognitionResultDTO recognitionConfig = newOcrFiledConfig.getOcrRecognitionResultByCityWithFallback(cityId, imgType);
            if (recognitionConfig == null) {
                logger.warn("buildOcrProcessContext","未找到OCR识别配置，cityId: {}, imgType: {}", cityId, imgType);
                return null;
            }

            return new OcrProcessContext(cityId, imgType, imgUrl, processedUrl, requestType, recognitionConfig);
        } catch (Exception e) {
            logger.error("buildOcrProcessContext","构建OCR处理上下文时发生异常", e);
            return null;
        }
    }

    /**
     * 尝试从缓存获取OCR结果
     */
    private Result<OcrRecoResultDTO> tryGetCachedOcrResult(OcrProcessContext context) {
        if (context.getRequestType() == null) {
            logger.warn("tryGetCachedOcrResult","无法映射imgType到requestType，跳过缓存查询，imgType: {}", context.getImgType());
            return null;
        }

        try {
            OverseasOcrRecordPO existingRecord = overseasOcrRecordRepository.queryOverseasOcrRecordList(
                context.getCityId(), context.getRequestType(), context.getProcessedUrl());

            if (existingRecord != null && StringUtils.isNotBlank(existingRecord.getResponseResult())) {
                logger.info("tryGetCachedOcrResult","找到已存在的OCR识别结果，直接返回，cityId: {}, requestType: {}, imgUrl: {}",
                           context.getCityId(), context.getRequestType(), context.getProcessedUrl());
                Cat.logEvent("OCR.Cache.Hit", "existing_record_found");

                List<OcrResultDTO> cachedResults = parseOcrResultFromRecord(existingRecord, context.getImgType());

                // 构建OcrRecoResultDTO
                OcrRecoResultDTO ocrRecoResultDTO = new OcrRecoResultDTO();
                ocrRecoResultDTO.setOcrId(existingRecord.getId());
                ocrRecoResultDTO.setOcrResultDTOList(cachedResults);

                return Result.Builder.<OcrRecoResultDTO>newResult().success().withData(ocrRecoResultDTO).build();
            }
        } catch (Exception e) {
            logger.warn("tryGetCachedOcrResult","查询已存在OCR记录时发生异常，继续执行OCR识别，cityId: {}, requestType: {}, imgUrl: {}",
                       context.getCityId(), context.getRequestType(), context.getProcessedUrl(), e);
            Cat.logEvent("OCR.Cache.Error", "query_existing_record_failed");
        }

        return null;
    }

    /**
     * 执行OCR识别
     */
    private Result<OcrRecoResultDTO> performOcrRecognition(OcrProcessContext context) {
        try {
            long imageId = localSnowFlakeUtil.nextId();

            // 构建AI平台请求
            AiPlatformRequest aiRequest = buildAiPlatformRequest(context, imageId);

            // 调用AI平台
            AiPlatformResponse aiResponse = callAiPlatform(aiRequest);

            // 处理AI平台响应
            return processAiPlatformResponse(context, aiResponse, imageId);

        } catch (Exception e) {
            logger.error("performOcrRecognition","执行OCR识别时发生异常，cityId: {}, imgType: {}",
                        context.getCityId(), context.getImgType(), e);
            Cat.logError("OCR识别异常", e);
            return buildFailResult("OCR识别异常: " + e.getMessage(), "OCR_RECOGNITION_EXCEPTION");
        }
    }

    /**
     * 构建失败结果
     */
    private Result<OcrRecoResultDTO> buildFailResult(String message, String code) {
        return Result.Builder.<OcrRecoResultDTO>newResult().fail()
                .withCode(code)
                .withMsg(message)
                .withData(null).build();
    }

    /**
     * 构建AI平台请求
     */
    private AiPlatformRequest buildAiPlatformRequest(OcrProcessContext context, long imageId) {
        Map<String, String> extData = new HashMap<>(16);
        extData.put("imageId", String.valueOf(imageId));
        extData.put("image", context.getProcessedUrl());
        extData.put("inputType", "url");
        extData.put("mode", "extract");
        extData.put("imageLabel", context.getRecognitionConfig().getReqType());

        AiPlatformRequest aiPlatformRequest = new AiPlatformRequest();
        aiPlatformRequest.setBiztype("voucherImageRec");
        aiPlatformRequest.setAppId("100025330");
        aiPlatformRequest.setService("imageService");
        aiPlatformRequest.setRequestData(JsonUtil.toJson(extData));

        return aiPlatformRequest;
    }

    /**
     * 调用AI平台
     */
    private AiPlatformResponse callAiPlatform(AiPlatformRequest aiRequest) throws Exception {
        // RPC请求日志
        logger.info("callAiPlatform","AI平台OCR识别RPC请求开始，请求参数: {}", JsonUtil.toJson(aiRequest));
        Cat.logEvent("RPC.Request", "AiPlatformService.execute");

        AiPlatformResponse response = aiPlatformServiceClient.execute(aiRequest);

        // RPC响应日志
        logger.info("callAiPlatform","AI平台OCR识别RPC请求完成，响应结果: {}", JsonUtil.toJson(response));
        Cat.logEvent("RPC.Response", "AiPlatformService.execute");

        return response;
    }

    /**
     * 处理AI平台响应
     */
    private Result<OcrRecoResultDTO> processAiPlatformResponse(OcrProcessContext context, AiPlatformResponse aiResponse, long imageId) {
        Integer returnCode = aiResponse.getReturnCode();
        if (!Objects.equals(returnCode, 200)) {
            logger.error("processAiPlatformResponse","AI平台RPC调用失败，返回码: {}, RPC响应: {}", returnCode, JsonUtil.toJson(aiResponse));
            Cat.logError("RPC调用失败", new RuntimeException("RPC return code: " + returnCode));
            return buildFailResult("AI平台RPC调用失败，返回码: " + returnCode, "RPC_CALL_FAILED");
        }

        String responseData = aiResponse.getResponseData();
        OcrRecognitionDTO ocrRecognitionDTO = JsonUtil.fromJson(responseData, new TypeReference<OcrRecognitionDTO>() {});
        String ocrReturnCode = ocrRecognitionDTO.getReturn_code();

        if (!StringUtils.equals(ocrReturnCode, "200")) {
            logger.error("processAiPlatformResponse","AI平台OCR识别失败，返回码: {}, OCR响应: {}", ocrReturnCode, JsonUtil.toJson(ocrRecognitionDTO));
            Cat.logError("OCR识别失败", new RuntimeException("OCR return code: " + ocrReturnCode));
            return buildFailResult("AI平台OCR识别失败，返回码: " + ocrReturnCode, "OCR_RECOGNITION_FAILED");
        }

        // 解析OCR结果
        List<ExtractResult> resExtract = ocrRecognitionDTO.getRes_extract();
        Map<String, String> extractedData = Optional.ofNullable(resExtract).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(ExtractResult::getKey, ExtractResult::getValue));

        // 构建返回结果
        List<OcrResultDTO> resultList = buildOcrResultList(context, extractedData, imageId);

        // 存储到数据库
        Long recordId = saveOcrResultToDatabase(context, extractedData, resultList);

        // 构建OcrRecoResultDTO
        OcrRecoResultDTO ocrRecoResultDTO = new OcrRecoResultDTO();
        ocrRecoResultDTO.setOcrId(recordId != null ? recordId : imageId);
        ocrRecoResultDTO.setOcrResultDTOList(resultList);

        return Result.Builder.<OcrRecoResultDTO>newResult().success().withData(ocrRecoResultDTO).build();
    }

    /**
     * 构建OCR结果列表
     */
    private List<OcrResultDTO> buildOcrResultList(OcrProcessContext context, Map<String, String> extractedData, long imageId) {
        List<OcrResultDTO> resultList = new ArrayList<>();
        List<OcrRespDTO> responseConfig = context.getRecognitionConfig().getResponse();

        if (CollectionUtils.isNotEmpty(responseConfig)) {
            responseConfig.forEach(ocrRespDTO -> {
                String field = ocrRespDTO.getField();
                String value = extractedData.get(field);
                OcrResultDTO ocrResultDTO = new OcrResultDTO();
                ocrResultDTO.setFieldName(field);
                ocrResultDTO.setBackFillField(ocrRespDTO.getBackFillField());
                ocrResultDTO.setValue(value);
                ocrResultDTO.setBackfill(ocrRespDTO.getBackfill());
                ocrResultDTO.setOcrId(imageId);
                resultList.add(ocrResultDTO);
            });
        }

        return resultList;
    }

    /**
     * 保存OCR结果到数据库
     * @return 返回数据库记录ID，如果保存失败返回null
     */
    private Long saveOcrResultToDatabase(OcrProcessContext context, Map<String, String> extractedData, List<OcrResultDTO> resultList) {
        if (context.getRequestType() == null) {
            logger.warn("saveOcrResultToDatabase","requestType为空，跳过数据库存储");
            return null;
        }

        try {
            OverseasOcrRecordPO record = new OverseasOcrRecordPO();
            record.setRequestType(context.getRequestType());
            record.setRequestImg(context.getProcessedUrl());
            record.setRequestCityId(context.getCityId());
            record.setResponseResult(JsonUtil.toJson(extractedData));
            record.setCreateUser(StringUtils.isEmpty(SessionHolder.getRestSessionAccountName()) ? Constant.SYSTEM : SessionHolder.getRestSessionAccountName());
            record.setModifyUser(record.getCreateUser());
            record.setDatachangeCreatetime(DateUtil.getNow());

            Long recordId = overseasOcrRecordRepository.insert(record);
            logger.info("saveOcrResultToDatabase","OCR识别结果已存储到数据库，recordId: {}, cityId: {}, requestType: {}, imgUrl: {}",
                       recordId, context.getCityId(), context.getRequestType(), context.getProcessedUrl());
            Cat.logEvent("OCR.Storage.Success", "record_saved");

            // 更新结果中的ocrId为数据库记录ID
            resultList.forEach(dto -> dto.setOcrId(recordId));
            return recordId;
        } catch (Exception e) {
            logger.error("saveOcrResultToDatabase","存储OCR识别结果到数据库时发生异常，cityId: {}, requestType: {}, imgUrl: {}",
                       context.getCityId(), context.getRequestType(), context.getProcessedUrl(), e);
            Cat.logError("OCR存储异常", e);
            // 存储失败不影响返回结果，继续执行
            return null;
        }
    }

}
