package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2021-09-09
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "drv_health_punch")
public class DrvHealthPunchPO implements DalPojo {

    /**
     * 主键ID
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 司机ID
     */
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;

    /**
     * 健康码状态(1.绿码,2.黄码,3.红码)
     */
	@Column(name = "health_code")
	@Type(value = Types.TINYINT)
	private Integer healthCode;

    /**
     * 健康码截图
     */
	@Column(name = "health_code_img")
	@Type(value = Types.VARCHAR)
	private String healthCodeImg;

    /**
     * 体温(0.37度以上,1.正常)
     */
	@Column(name = "temperature")
	@Type(value = Types.TINYINT)
	private Integer temperature;

    /**
     * 车辆消毒(0.未消毒,1.已消毒)
     */
	@Column(name = "vehicle_disinfection")
	@Type(value = Types.TINYINT)
	private Integer vehicleDisinfection;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 操作人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * 是否展示
	 */
	@Column(name = "is_show_place_qr_code")
	@Type(value = Types.BIT)
	private Boolean isShowPlaceQRCode;

	/**
	 * 场景码
	 */
	@Column(name = "place_qr_code")
	@Type(value = Types.VARCHAR)
	private String placeQRCode;

	/**
	 * 抗原报告
	 */
	@Column(name = "antigen_report")
	@Type(value = Types.TINYINT)
	private Integer antigenReport;

	/**
	 * 核酸检测报告
	 */
	@Column(name = "nucleic_report")
	@Type(value = Types.TINYINT)
	private Integer nucleicReport;

	/**
	 * 抗原报告图片
	 */
	@Column(name = "antigen_report_img")
	@Type(value = Types.VARCHAR)
	private String antigenReportImg;

	/**
	 * 核酸检测报告图片
	 */
	@Column(name = "nucleic_report_img")
	@Type(value = Types.VARCHAR)
	private String nucleicReportImg;

	/**
	 * 健康打卡扩展字段
	 */
	@Column(name = "extend_info")
	@Type(value = Types.VARCHAR)
	private String extendInfo;

	public String getExtendInfo() {
		return extendInfo;
	}

	public void setExtendInfo(String extendInfo) {
		this.extendInfo = extendInfo;
	}

	public Integer getAntigenReport() {
		return antigenReport;
	}

	public void setAntigenReport(Integer antigenReport) {
		this.antigenReport = antigenReport;
	}

	public Integer getNucleicReport() {
		return nucleicReport;
	}

	public void setNucleicReport(Integer nucleicReport) {
		this.nucleicReport = nucleicReport;
	}

	public String getAntigenReportImg() {
		return antigenReportImg;
	}

	public void setAntigenReportImg(String antigenReportImg) {
		this.antigenReportImg = antigenReportImg;
	}

	public String getNucleicReportImg() {
		return nucleicReportImg;
	}

	public void setNucleicReportImg(String nucleicReportImg) {
		this.nucleicReportImg = nucleicReportImg;
	}

	public Boolean getShowPlaceQRCode() {
		return isShowPlaceQRCode;
	}

	public void setShowPlaceQRCode(Boolean showPlaceQRCode) {
		isShowPlaceQRCode = showPlaceQRCode;
	}

	public String getPlaceQRCode() {
		return placeQRCode;
	}

	public void setPlaceQRCode(String placeQRCode) {
		this.placeQRCode = placeQRCode;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDrvId() {
		return drvId;
	}

	public void setDrvId(Long drvId) {
		this.drvId = drvId;
	}

	public Integer getHealthCode() {
		return healthCode;
	}

	public void setHealthCode(Integer healthCode) {
		this.healthCode = healthCode;
	}

	public String getHealthCodeImg() {
		return healthCodeImg;
	}

	public void setHealthCodeImg(String healthCodeImg) {
		this.healthCodeImg = healthCodeImg;
	}

	public Integer getTemperature() {
		return temperature;
	}

	public void setTemperature(Integer temperature) {
		this.temperature = temperature;
	}

	public Integer getVehicleDisinfection() {
		return vehicleDisinfection;
	}

	public void setVehicleDisinfection(Integer vehicleDisinfection) {
		this.vehicleDisinfection = vehicleDisinfection;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

}