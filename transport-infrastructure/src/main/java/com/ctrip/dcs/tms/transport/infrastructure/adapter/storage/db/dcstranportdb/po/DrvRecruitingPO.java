package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-04-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "drv_recruiting")
public class DrvRecruitingPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "drv_recruiting_id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long drvRecruitingId;

    /**
     * 供应商ID
     */
	@Column(name = "supplier_id")
	@Type(value = Types.BIGINT)
	private Long supplierId;

    /**
     * 司机姓名
     */
	@Column(name = "drv_name")
	@Type(value = Types.VARCHAR)
	private String drvName;

    /**
     * 司机英文姓名
     */
	@Column(name = "drv_english_name")
	@Type(value = Types.VARCHAR)
	private String drvEnglishName;

    /**
     * 国家码
     */
	@Column(name = "country_id")
	@Type(value = Types.BIGINT)
	private Long countryId;

    /**
     * 国家名称
     */
	@Column(name = "country_name")
	@Type(value = Types.VARCHAR)
	private String countryName;

    /**
     * 城市
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 司机语言(语言ID逗号分隔)
     */
	@Column(name = "drv_language")
	@Type(value = Types.VARCHAR)
	private String drvLanguage;

    /**
     * 车辆ID
     */
	@Column(name = "vehicle_id")
	@Type(value = Types.BIGINT)
	private Long vehicleId;

    /**
     * 车牌号
     */
	@Column(name = "vehicle_license")
	@Type(value = Types.VARCHAR)
	private String vehicleLicense;

    /**
     * 国际区号，如中国国际区号是+86
     */
	@Column(name = "igt_code")
	@Type(value = Types.VARCHAR)
	private String igtCode;

    /**
     * 手机号
     */
	@Column(name = "drv_phone")
	@Type(value = Types.VARCHAR)
	private String drvPhone;

    /**
     * 身份证号
     */
	@Column(name = "drv_idcard")
	@Type(value = Types.VARCHAR)
	private String drvIdcard;

    /**
     * 登录账号
     */
	@Column(name = "login_account")
	@Type(value = Types.VARCHAR)
	private String loginAccount;

    /**
     * 邮箱
     */
	@Column(name = "email")
	@Type(value = Types.VARCHAR)
	private String email;

    /**
     * 微信
     */
	@Column(name = "wechat")
	@Type(value = Types.VARCHAR)
	private String wechat;

    /**
     * 准驾车型
     */
	@Column(name = "quasi_driving_type")
	@Type(value = Types.VARCHAR)
	private String quasiDrivingType;

    /**
     * 意愿车型(车型ID,多个逗号分隔)
     */
	@Column(name = "intend_vehicle_type_id")
	@Type(value = Types.VARCHAR)
	private String intendVehicleTypeId;

    /**
     * 证件号码
     */
	@Column(name = "certificate_number")
	@Type(value = Types.VARCHAR)
	private String certificateNumber;

    /**
     * 初次领证日期
     */
	@Column(name = "certi_date")
	@Type(value = Types.DATE)
	private Date certiDate;

    /**
     * 驾驶证有效期限开始时间
     */
	@Column(name = "expiry_begin_date")
	@Type(value = Types.DATE)
	private Date expiryBeginDate;

    /**
     * 驾驶证有效期限结束时间
     */
	@Column(name = "expiry_end_date")
	@Type(value = Types.DATE)
	private Date expiryEndDate;

    /**
     * 驾驶证图片地址
     */
	@Column(name = "drvcard_img")
	@Type(value = Types.VARCHAR)
	private String drvcardImg;

    /**
     * 身份证图片地址
     */
	@Column(name = "idcard_img")
	@Type(value = Types.VARCHAR)
	private String idcardImg;

    /**
     * 头像
     */
	@Column(name = "drv_head_img")
	@Type(value = Types.VARCHAR)
	private String drvHeadImg;

    /**
     * 人车合照
     */
	@Column(name = "people_vehicle_img")
	@Type(value = Types.VARCHAR)
	private String peopleVehicleImg;

    /**
     * 网约车人证照片
     */
	@Column(name = "net_vehicle_peo_img")
	@Type(value = Types.VARCHAR)
	private String netVehiclePeoImg;

    /**
     * 司机正式表ID
     */
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;

    /**
     * 是否国内。0:国内，1:国际
     */
	@Column(name = "internal_scope")
	@Type(value = Types.TINYINT)
	private Integer internalScope;

    /**
     * 司机来源(1司机自助、2人工录入)
     */
	@Column(name = "drv_from")
	@Type(value = Types.TINYINT)
	private Integer drvFrom;

    /**
     * 0:招募司机初始提交状态 1:供应商驳回 4:供应商提交/供应商通过/待运营审批 6:运营驳回 10:通过并落库
     */
	@Column(name = "approver_status")
	@Type(value = Types.TINYINT)
	private Integer approverStatus;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 修改人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * 车型ID
	 */
	@Column(name = "vehicle_type_id")
	@Type(value = Types.BIGINT)
	private Long vehicleTypeId;

	/**
	 * 司机密码
     */
	@Column(name = "login_pwd")
	@Type(value = Types.VARCHAR)
	private String loginPwd;

	/**
	 * salt
	 */
	@Column(name = "salt")
	@Type(value = Types.VARCHAR)
	private String salt;

	/**
	 * 合作模式。1.全职，2.兼职
	 */
	@Column(name = "coop_mode")
	@Type(value = Types.TINYINT)
	private Integer coopMode;

	/**
	 * 无罪证明
	 */
	@Column(name = "no_criminal_proof_img")
	@Type(value = Types.VARCHAR)
	private String noCriminalProofImg;

	/**
	 * 身份证背面图片
	 */
	@Column(name = "idcard_back_img")
	@Type(value = Types.VARCHAR)
	private String idcardBackImg;

	/**
	 * 工作时段
	 */
	@Column(name = "work_period")
	@Type(value = Types.VARCHAR)
	private String workPeriod;


	/**
	 * 性别
	 */
	@Column(name = "sex")
	@Type(value = Types.VARCHAR)
	private String sex;

	/**
	 * 民族
	 */
	@Column(name = "nation")
	@Type(value = Types.VARCHAR)
	private String nation;

	/**
	 * 出生日期
	 */
	@Column(name = "birthday")
	@Type(value = Types.DATE)
	private Date birthday;

	/**
	 * 身份证有效期
	 */
	@Column(name = "idcard_validity")
	@Type(value = Types.DATE)
	private Date idcardValidity;

	/**
	 * 驾驶证号
	 */
	@Column(name = "drv_license_number")
	@Type(value = Types.VARCHAR)
	private String drvLicenseNumber;

	/**
	 * 驾驶证姓名
	 */
	@Column(name = "drv_license_name")
	@Type(value = Types.VARCHAR)
	private String drvLicenseName;

	/**
	 * 其它证件
	 */
	@Column(name = "other_certificate_img")
	@Type(value = Types.VARCHAR)
	private String otherCertificateImg;

	/**
	 * 校验状态 1.已完成,2.未完成
	 */
	@Column(name = "check_status")
	@Type(value = Types.TINYINT)
	private Integer checkStatus;

	/**
	 * 逻辑产线
	 */
	@Column(name = "category_synthesize_code")
	@Type(value = Types.TINYINT)
	private Integer categorySynthesizeCode;

	/**
	 * 核酸检测时间
	 */
	@Column(name = "nucleic_acid_testing_time")
	@Type(value = Types.DATE)
	private Date nucleicAcidTestingTime;

	/**
	 * 核酸检测时间
	 */
	@Column(name = "vaccination_time_list")
	@Type(value = Types.VARCHAR)
	private String vaccinationTimeList;

	/**
	 * 核酸报告图片
	 */
	@Column(name = "nucleic_acid_report_img")
	@Type(value = Types.VARCHAR)
	private String nucleicAcidReportImg;

	/**
	 * 疫苗报告图片
	 */
	@Column(name = "vaccine_report_img")
	@Type(value = Types.VARCHAR)
	private String vaccineReportImg;

	/**
	 * OCR提取报告疫苗
	 */
	@Column(name = "ocr_vaccine_data")
	@Type(value = Types.VARCHAR)
	private String ocrVaccineData;

	/**
	 * OCR提取报告核酸
	 */
	@Column(name = "ocr_nucleic_acid_data")
	@Type(value = Types.VARCHAR)
	private String ocrNucleicAcidData;

	/**
	 * 网约车申诉材料
	 */
	@Column(name = "net_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String netAppealMaterials;

	/**
	 * 审核进度（1.已完成,0,未完成）
	 */
	@Column(name = "approve_schedule")
	@Type(value = Types.INTEGER)
	private Integer approveSchedule;

	/**
	 * 审核时效（1未超时，0.已超时）
	 */
	@Column(name = "approve_aging")
	@Type(value = Types.INTEGER)
	private Integer approveAging;

	/**
	 * 版本标识(1.第一版,2.第二版)
	 */
	@Column(name = "version_flag")
	@Type(value = Types.INTEGER)
	private Integer versionFlag;

	/**
	 * 审批时间
	 */
	@Column(name = "approve_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp approveTime;

	/**
	 * 保存最初的证件配置值
	 */
	@Column(name = "certificate_config")
	@Type(value = Types.VARCHAR)
	private String certificateConfig;

	/**
	 * OCR识别值
	 */
	@Column(name = "ocr_field_value")
	@Type(value = Types.VARCHAR)
	private String ocrFieldValue;

	/**
	 * 是否经过BD审批(0-未经过,1-已经过)
	 */
	@Column(name = "bd_approve_status")
	@Type(value = Types.INTEGER)
	private Integer bdApproveStatus;

	/**
	 * 供应商审核进度（1.已完成,0,未完成）
	 */
	@Column(name = "supplier_approve_schedule")
	@Type(value = Types.INTEGER)
	private Integer supplierApproveSchedule;

	/**
	 * 三方参数请求快照
	 */
	@Column(name = "mod_snapshot_values")
	@Type(value = Types.VARCHAR)
	private String modSnapshotValues;

	/**
	 * 身份证申诉材料
	 */
	@Column(name = "idcard_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String idcardAppealMaterials;

	/**
	 * 驾驶证申诉材料
	 */
	@Column(name = "drv_license_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String drvLicenseAppealMaterials;

	/**
	 * 平台驳回次数
	 */
	@Column(name = "bd_turn_down_count")
	@Type(value = Types.INTEGER)
	private Integer bdTurnDownCount;

	/**
	 * 1表示启用，0表示删除
	 */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

	/**
	 * OCR是否通过,0.不通过,1-通过，默认不通过
	 */
	@Column(name = "ocr_pass_status")
	@Type(value = Types.INTEGER)
	private Integer ocrPassStatus;

	/**
	 * OCR各项校验结果
	 */
	@Column(name = "ocr_pass_status_json")
	@Type(value = Types.VARCHAR)
	private String ocrPassStatusJson;


	/**
	 * 派安盈账户(境外)
	 */
	@Column(name = "paiay_account")
	@Type(value = Types.VARCHAR)
	private String paiayAccount;

	/**
	 * 派安盈邮箱
	 */
	@Column(name = "paiay_email")
	@Type(value = Types.VARCHAR)
	private String paiayEmail;

	/**
	 * 境外司机入注存储的密码(可解密)
	 */
	@Column(name = "drv_pwd")
	@Type(value = Types.VARCHAR)
	private String drvPwd;

	/**
	 * 现场照片
	 */
	@Column(name = "scene_photo")
	@Type(value = Types.VARCHAR)
	private String scenePhoto;

	/**
	 * 上云UDl
	 */
	@Column(name = "provider_data_location")
	@Type(value = Types.VARCHAR)
	private String providerDataLocation;

	/**
	 * 是否taxi司机 true 为是 false为否
	 */
	@Column(name = "is_taxi")
	@Type(value = Types.TINYINT)
	public Integer isTaxi;

	/**
	 * 新OCR识别值
	 */
	@Column(name = "new_ocr_field_value")
	@Type(value = Types.VARCHAR)
	public String newOcrFieldValue;


	public String getProviderDataLocation() {
		return providerDataLocation;
	}

	public void setProviderDataLocation(String providerDataLocation) {
		this.providerDataLocation = providerDataLocation;
	}

	public String getDrvPwd() {
		return drvPwd;
	}

	public void setDrvPwd(String drvPwd) {
		this.drvPwd = drvPwd;
	}

	public String getPaiayAccount() {
		return paiayAccount;
	}

	public void setPaiayAccount(String paiayAccount) {
		this.paiayAccount = paiayAccount;
	}

	public String getPaiayEmail() {
		return paiayEmail;
	}

	public void setPaiayEmail(String paiayEmail) {
		this.paiayEmail = paiayEmail;
	}

	public Integer getOcrPassStatus() {
		return ocrPassStatus;
	}

	public void setOcrPassStatus(Integer ocrPassStatus) {
		this.ocrPassStatus = ocrPassStatus;
	}

	public String getOcrPassStatusJson() {
		return ocrPassStatusJson;
	}

	public void setOcrPassStatusJson(String ocrPassStatusJson) {
		this.ocrPassStatusJson = ocrPassStatusJson;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public String getIdcardAppealMaterials() {
		return idcardAppealMaterials;
	}

	public void setIdcardAppealMaterials(String idcardAppealMaterials) {
		this.idcardAppealMaterials = idcardAppealMaterials;
	}

	public String getDrvLicenseAppealMaterials() {
		return drvLicenseAppealMaterials;
	}

	public void setDrvLicenseAppealMaterials(String drvLicenseAppealMaterials) {
		this.drvLicenseAppealMaterials = drvLicenseAppealMaterials;
	}

	public Integer getBdTurnDownCount() {
		return bdTurnDownCount;
	}

	public void setBdTurnDownCount(Integer bdTurnDownCount) {
		this.bdTurnDownCount = bdTurnDownCount;
	}

	public String getModSnapshotValues() {
		return modSnapshotValues;
	}

	public void setModSnapshotValues(String modSnapshotValues) {
		this.modSnapshotValues = modSnapshotValues;
	}

	public Integer getSupplierApproveSchedule() {
		return supplierApproveSchedule;
	}

	public void setSupplierApproveSchedule(Integer supplierApproveSchedule) {
		this.supplierApproveSchedule = supplierApproveSchedule;
	}

	public Integer getBdApproveStatus() {
		return bdApproveStatus;
	}

	public void setBdApproveStatus(Integer bdApproveStatus) {
		this.bdApproveStatus = bdApproveStatus;
	}

	public String getOcrFieldValue() {
		return ocrFieldValue;
	}

	public void setOcrFieldValue(String ocrFieldValue) {
		this.ocrFieldValue = ocrFieldValue;
	}

	public String getCertificateConfig() {
		return certificateConfig;
	}

	public void setCertificateConfig(String certificateConfig) {
		this.certificateConfig = certificateConfig;
	}

	public String getNetAppealMaterials() {
		return netAppealMaterials;
	}

	public void setNetAppealMaterials(String netAppealMaterials) {
		this.netAppealMaterials = netAppealMaterials;
	}

	public String getOcrVaccineData() {
		return ocrVaccineData;
	}

	public void setOcrVaccineData(String ocrVaccineData) {
		this.ocrVaccineData = ocrVaccineData;
	}

	public String getOcrNucleicAcidData() {
		return ocrNucleicAcidData;
	}

	public void setOcrNucleicAcidData(String ocrNucleicAcidData) {
		this.ocrNucleicAcidData = ocrNucleicAcidData;
	}

	public String getNucleicAcidReportImg() {
		return nucleicAcidReportImg;
	}

	public void setNucleicAcidReportImg(String nucleicAcidReportImg) {
		this.nucleicAcidReportImg = nucleicAcidReportImg;
	}

	public String getVaccineReportImg() {
		return vaccineReportImg;
	}

	public void setVaccineReportImg(String vaccineReportImg) {
		this.vaccineReportImg = vaccineReportImg;
	}

	public Date getNucleicAcidTestingTime() {
		return nucleicAcidTestingTime;
	}

	public void setNucleicAcidTestingTime(Date nucleicAcidTestingTime) {
		this.nucleicAcidTestingTime = nucleicAcidTestingTime;
	}

	public String getVaccinationTimeList() {
		return vaccinationTimeList;
	}

	public void setVaccinationTimeList(String vaccinationTimeList) {
		this.vaccinationTimeList = vaccinationTimeList;
	}

	public Integer getCategorySynthesizeCode() {
		return categorySynthesizeCode;
	}

	public void setCategorySynthesizeCode(Integer categorySynthesizeCode) {
		this.categorySynthesizeCode = categorySynthesizeCode;
	}

	public Long getDrvRecruitingId() {
		return drvRecruitingId;
	}

	public void setDrvRecruitingId(Long drvRecruitingId) {
		this.drvRecruitingId = drvRecruitingId;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public String getDrvName() {
		return drvName;
	}

	public void setDrvName(String drvName) {
		this.drvName = drvName;
	}

	public String getDrvEnglishName() {
		return drvEnglishName;
	}

	public void setDrvEnglishName(String drvEnglishName) {
		this.drvEnglishName = drvEnglishName;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public String getDrvLanguage() {
		return drvLanguage;
	}

	public void setDrvLanguage(String drvLanguage) {
		this.drvLanguage = drvLanguage;
	}

	public Long getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(Long vehicleId) {
		this.vehicleId = vehicleId;
	}

	public String getVehicleLicense() {
		return vehicleLicense;
	}

	public void setVehicleLicense(String vehicleLicense) {
		this.vehicleLicense = vehicleLicense;
	}

	public String getIgtCode() {
		return igtCode;
	}

	public void setIgtCode(String igtCode) {
		this.igtCode = igtCode;
	}

	public String getDrvPhone() {
		return drvPhone;
	}

	public void setDrvPhone(String drvPhone) {
		this.drvPhone = drvPhone;
	}

	public String getDrvIdcard() {
		return drvIdcard;
	}

	public void setDrvIdcard(String drvIdcard) {
		this.drvIdcard = drvIdcard;
	}

	public String getLoginAccount() {
		return loginAccount;
	}

	public void setLoginAccount(String loginAccount) {
		this.loginAccount = loginAccount;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getWechat() {
		return wechat;
	}

	public void setWechat(String wechat) {
		this.wechat = wechat;
	}

	public String getQuasiDrivingType() {
		return quasiDrivingType;
	}

	public void setQuasiDrivingType(String quasiDrivingType) {
		this.quasiDrivingType = quasiDrivingType;
	}

	public String getIntendVehicleTypeId() {
		return intendVehicleTypeId;
	}

	public void setIntendVehicleTypeId(String intendVehicleTypeId) {
		this.intendVehicleTypeId = intendVehicleTypeId;
	}

	public String getCertificateNumber() {
		return certificateNumber;
	}

	public void setCertificateNumber(String certificateNumber) {
		this.certificateNumber = certificateNumber;
	}

	public Date getCertiDate() {
		return certiDate;
	}

	public void setCertiDate(Date certiDate) {
		this.certiDate = certiDate;
	}

	public Date getExpiryBeginDate() {
		return expiryBeginDate;
	}

	public void setExpiryBeginDate(Date expiryBeginDate) {
		this.expiryBeginDate = expiryBeginDate;
	}

	public Date getExpiryEndDate() {
		return expiryEndDate;
	}

	public void setExpiryEndDate(Date expiryEndDate) {
		this.expiryEndDate = expiryEndDate;
	}

	public String getDrvcardImg() {
		return drvcardImg;
	}

	public void setDrvcardImg(String drvcardImg) {
		this.drvcardImg = drvcardImg;
	}

	public String getIdcardImg() {
		return idcardImg;
	}

	public void setIdcardImg(String idcardImg) {
		this.idcardImg = idcardImg;
	}

	public String getDrvHeadImg() {
		return drvHeadImg;
	}

	public void setDrvHeadImg(String drvHeadImg) {
		this.drvHeadImg = drvHeadImg;
	}

	public String getPeopleVehicleImg() {
		return peopleVehicleImg;
	}

	public void setPeopleVehicleImg(String peopleVehicleImg) {
		this.peopleVehicleImg = peopleVehicleImg;
	}

	public String getNetVehiclePeoImg() {
		return netVehiclePeoImg;
	}

	public void setNetVehiclePeoImg(String netVehiclePeoImg) {
		this.netVehiclePeoImg = netVehiclePeoImg;
	}

	public Long getDrvId() {
		return drvId;
	}

	public void setDrvId(Long drvId) {
		this.drvId = drvId;
	}

	public Integer getInternalScope() {
		return internalScope;
	}

	public void setInternalScope(Integer internalScope) {
		this.internalScope = internalScope;
	}

	public Integer getDrvFrom() {
		return drvFrom;
	}

	public void setDrvFrom(Integer drvFrom) {
		this.drvFrom = drvFrom;
	}

	public Integer getApproverStatus() {
		return approverStatus;
	}

	public void setApproverStatus(Integer approverStatus) {
		this.approverStatus = approverStatus;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Long getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(Long vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public String getLoginPwd() {
		return loginPwd;
	}

	public void setLoginPwd(String loginPwd) {
		this.loginPwd = loginPwd;
	}

	public String getSalt() {
		return salt;
	}

	public void setSalt(String salt) {
		this.salt = salt;
	}

	public Integer getCoopMode() {
		return coopMode;
	}

	public void setCoopMode(Integer coopMode) {
		this.coopMode = coopMode;
	}

	public String getNoCriminalProofImg() {
		return noCriminalProofImg;
	}

	public void setNoCriminalProofImg(String noCriminalProofImg) {
		this.noCriminalProofImg = noCriminalProofImg;
	}

	public String getIdcardBackImg() {
		return idcardBackImg;
	}

	public void setIdcardBackImg(String idcardBackImg) {
		this.idcardBackImg = idcardBackImg;
	}

	public String getWorkPeriod() {
		return workPeriod;
	}

	public void setWorkPeriod(String workPeriod) {
		this.workPeriod = workPeriod;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getNation() {
		return nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Date getIdcardValidity() {
		return idcardValidity;
	}

	public void setIdcardValidity(Date idcardValidity) {
		this.idcardValidity = idcardValidity;
	}

	public String getDrvLicenseNumber() {
		return drvLicenseNumber;
	}

	public void setDrvLicenseNumber(String drvLicenseNumber) {
		this.drvLicenseNumber = drvLicenseNumber;
	}

	public String getDrvLicenseName() {
		return drvLicenseName;
	}

	public void setDrvLicenseName(String drvLicenseName) {
		this.drvLicenseName = drvLicenseName;
	}

	public String getOtherCertificateImg() {
		return otherCertificateImg;
	}

	public void setOtherCertificateImg(String otherCertificateImg) {
		this.otherCertificateImg = otherCertificateImg;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Integer getApproveSchedule() {
		return approveSchedule;
	}

	public void setApproveSchedule(Integer approveSchedule) {
		this.approveSchedule = approveSchedule;
	}

	public Integer getApproveAging() {
		return approveAging;
	}

	public void setApproveAging(Integer approveAging) {
		this.approveAging = approveAging;
	}

	public Integer getVersionFlag() {
		return versionFlag;
	}

	public void setVersionFlag(Integer versionFlag) {
		this.versionFlag = versionFlag;
	}

	public Timestamp getApproveTime() {
		return approveTime;
	}

	public void setApproveTime(Timestamp approveTime) {
		this.approveTime = approveTime;
	}

	public String getScenePhoto() {
		return scenePhoto;
	}

	public void setScenePhoto(String scenePhoto) {
		this.scenePhoto = scenePhoto;
	}

	public Integer getTaxi() {
		return isTaxi;
	}

	public void setTaxi(Integer taxi) {
		isTaxi = taxi;
	}

	public String getNewOcrFieldValue() {
		return newOcrFieldValue;
	}

	public void setNewOcrFieldValue(String newOcrFieldValue) {
		this.newOcrFieldValue = newOcrFieldValue;
	}
}
