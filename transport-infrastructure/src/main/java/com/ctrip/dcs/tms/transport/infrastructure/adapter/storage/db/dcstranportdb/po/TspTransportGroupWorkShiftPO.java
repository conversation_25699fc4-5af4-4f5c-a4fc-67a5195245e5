package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 *
 * <AUTHOR>
 * @Date 2020/11/26 14:22
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tsp_transport_group_work_shift")
public class TspTransportGroupWorkShiftPO implements DalPojo {

    /**
     * 工作班次主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 班次名称
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 班次开始时间
     */
    @Column(name = "start_time")
    @Type(value = Types.VARCHAR)
    private String starTime;

    /**
     * 班次结束时间
     */
    @Column(name = "end_time")
    @Type(value = Types.VARCHAR)
    private String endTime;

    /**
     * 司机上线数
     */
    @Column(name = "driver_upper_limit")
    @Type(value = Types.BIGINT)
    private Long driverUpperLimit;

    /**
     * 运力组id
     */
    @Column(name = "transport_group_id")
    @Type(value = Types.BIGINT)
    private Long transportGroupId;

    /**
     * 是否有效（1：有效，0：无效）
     */
    @Column(name = "active")
    @Type(value = Types.INTEGER)
    private Integer active;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 操作人
     */
    @Column(name = "modify_user")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 排序
     */
    @Column(name = "work_shift_order")
    @Type(value = Types.INTEGER)
    private Integer order;

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStarTime() {
        return starTime;
    }

    public void setStarTime(String starTime) {
        this.starTime = starTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getDriverUpperLimit() {
        return driverUpperLimit;
    }

    public void setDriverUpperLimit(Long driverUpperLimit) {
        this.driverUpperLimit = driverUpperLimit;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
