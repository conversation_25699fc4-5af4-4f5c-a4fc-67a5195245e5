package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.List;
import java.util.Map;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.ExitInspectionDTO;
import org.springframework.stereotype.Component;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class ExitInspectionConfig {
    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(List.class, ExitInspectionDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);
    private static JsonConfig<Map<String, List<ExitInspectionDTO>>> complexTestJsonConfig = JsonConfig.get("exit.inspection.json", parameter);
    public Map<String, List<ExitInspectionDTO>> getExitInspectionMap() {
        return complexTestJsonConfig.current();
    }
}
