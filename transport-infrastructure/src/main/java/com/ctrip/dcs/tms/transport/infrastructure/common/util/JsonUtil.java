package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.core.type.*;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

/**
 * <AUTHOR>
 * @Date 2020/4/7 17:46
 */
public abstract class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
    }

    public static <T> T fromJson(String json, TypeReference<T> typeRef){
        try {
            T rst = (T)objectMapper.readValue(json, typeRef);
            return rst;
        } catch (Exception e) {
            throw new RuntimeException("Json Error",e);
        }
    }

    public static String toJson(Object obj){
        if(obj == null || obj instanceof String){
            return (String)obj;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("Json Error",e);
        }
    }

}
