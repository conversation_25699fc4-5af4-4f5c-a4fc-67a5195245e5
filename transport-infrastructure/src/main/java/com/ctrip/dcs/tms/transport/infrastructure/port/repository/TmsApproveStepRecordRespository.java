package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsApproveStepRecordRespository {

    Long insert(ApproveStepRecordPO approveStepRecordPO) throws SQLException;

    List<ApproveStepRecordPO> queryList(Long recruitingId,Integer recruitingType,List<Long> totalRecruitingIds);
}