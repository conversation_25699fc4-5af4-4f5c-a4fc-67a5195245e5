package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsCertificateConfigRepository {

    TmsCertificateConfigPO queryByPK(Long id);

    int countTmsCertificateConfig(List<Long> cityIds,List<Integer> productLines);

    List<TmsCertificateConfigPO> queryTmsCertificateConfiList(List<Long> cityIds,List<Integer> productLines,int beginPage,int pageSize);

    int updateCertificateConfig(Long id,String certificateConfig,String modifyUser,String vehicleTypeIds);

    List<TmsCertificateConfigPO> queryConfigByCityAndProLine(List<Long> cityIds,List<Integer> productLines);

    int batchAddCertificateConfig(List<TmsCertificateConfigPO> configPO) throws SQLException;

}