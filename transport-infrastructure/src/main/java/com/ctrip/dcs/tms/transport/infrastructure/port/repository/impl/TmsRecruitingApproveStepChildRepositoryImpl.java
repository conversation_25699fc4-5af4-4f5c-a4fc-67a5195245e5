package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 *
 */
@Repository(value = "tmsRecruitingApproveStepChildRepository")
public class TmsRecruitingApproveStepChildRepositoryImpl implements TmsRecruitingApproveStepChildRepository {

    private DalRepository<TmsRecruitingApproveStepChildPO> tmsRecruitingApproveStepChildRepo;

    public TmsRecruitingApproveStepChildRepositoryImpl() throws SQLException {
        tmsRecruitingApproveStepChildRepo = new DalRepositoryImpl<>(TmsRecruitingApproveStepChildPO.class);

    }

    @Override
    public TmsRecruitingApproveStepChildPO queryByPk(Long id) {
        return tmsRecruitingApproveStepChildRepo.queryByPk(id);
    }

    @Override
    public int update(TmsRecruitingApproveStepChildPO childPO) {
        return tmsRecruitingApproveStepChildRepo.update(childPO);
    }

    @Override
    public Long insert(TmsRecruitingApproveStepChildPO stepChildPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsRecruitingApproveStepChildRepo.insert(new DalHints(), keyHolder, stepChildPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<TmsRecruitingApproveStepChildPO> queryChildByStepIdList(List<Long> stepIdList) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("recruiting_approve_step_id", stepIdList, Types.BIGINT);
            builder.orderBy("child_item", true);
            return tmsRecruitingApproveStepChildRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateChildCheckStatus(Long id, Integer checkStatus,String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        if(checkStatus == null){
            return 0;
        }
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step_child set modify_user = ?, check_status = ? where id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepChildRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsRecruitingApproveStepChildPO> queryChildByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("id", ids, Types.BIGINT);
            builder.orderBy("datachange_lasttime", false);
            return tmsRecruitingApproveStepChildRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
