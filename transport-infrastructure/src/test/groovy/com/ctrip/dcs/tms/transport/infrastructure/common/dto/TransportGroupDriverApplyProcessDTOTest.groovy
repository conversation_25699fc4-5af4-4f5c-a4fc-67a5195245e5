package com.ctrip.dcs.tms.transport.infrastructure.common.dto

import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupApplyStatusSOARequestType
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverPoint
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.ApplyDriverRelationDetailPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupWorkShiftPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([TmsTransportConstant.TransportDriverApplyRuleEnum])
@SuppressStaticInitializationFor(["com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.TransportDriverApplyRuleEnum"])
class TransportGroupDriverApplyProcessDTOTest extends Specification {
    def testObj = new TransportGroupDriverApplyProcessDTO()
    def applyDriverMap = Mock(Map)
    def workshiftApplySuccessDriverListMap = Mock(Map)
    def workshiftFailedDriverListMap = Mock(Map)
    def driverPointMap = Mock(Map)
    def workShiftDriverRelationDetailPOMap = Mock(LinkedHashMap)
    def transportGroupDriverApplyFailedReasonDTOMap = Mock(Map)
    def workSiftDriverApplicationMaximumLimitMap = Mock(Map)
    def driverWorkPeriodMap = Mock(Map)


    @Unroll
    def "setLeaveHourTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        when:
        def result = spy.setLeaveHour(drvId, leaveBeginTime, leaveEndTime, duration)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        duration | drvId | leaveBeginTime                            | leaveEndTime                              || expectedResult
        0L       | 1L    | new Timestamp(System.currentTimeMillis()) | new Timestamp(System.currentTimeMillis()) || null
    }

    @Unroll
    def "setFreezeHourTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        when:
        def result = spy.setFreezeHour(drvId, firstFreezeTime, freezeEndDateTime, freezeHour)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        freezeEndDateTime                         | freezeHour | drvId | firstFreezeTime                           || expectedResult
        new Timestamp(System.currentTimeMillis()) | 0L         | 1L    | new Timestamp(System.currentTimeMillis()) || null
    }

    @Unroll
    def "setDriverPointInfoTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        spy.getKey(_, _) >> "getKeyResponse"
        and: "Mock 静态方法"
        mockStatic(TmsTransportConstant.TransportDriverApplyRuleEnum)
        when(TmsTransportConstant.TransportDriverApplyRuleEnum.values()).thenReturn([TmsTransportConstant.TransportDriverApplyRuleEnum.A] as TmsTransportConstant.TransportDriverApplyRuleEnum[])
        when:
        def result = spy.setDriverPointInfo(drvId, driverPoint, driverRankingInCity, driverRankingInBatch, workShiftId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverRankingInBatch | drvId | driverPoint | driverRankingInCity | workShiftId || expectedResult
        1L                   | 1L    | 0d          | 1L                  | 1L          || null
    }

    @Unroll
    def "setApplyResultTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        spy.setTransportGroupDriverApplyFailedReasonDTOMap(new HashMap<String, TransportGroupDriverApplyFailedReasonDTO>())
        when:
        def result = spy.setApplyResult(drvId, applyStatus, applyFailedType, ruleDescription, 1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | applyStatus                              | ruleDescription   | applyFailedType                                                                 || expectedResult
        1L    | TmsTransportConstant.ApplyStatusEnum.NOT | "ruleDescription" | TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT || null
    }

    @Unroll
    def "setExistDataApplyResultTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_, _) >> "getKeyResponse"
        and: "Mock 静态方法"
        mockStatic(TmsTransportConstant.TransportDriverApplyRuleEnum)
        when(TmsTransportConstant.TransportDriverApplyRuleEnum.values()).thenReturn([TmsTransportConstant.TransportDriverApplyRuleEnum.A] as TmsTransportConstant.TransportDriverApplyRuleEnum[])
        when:
        def result = spy.setApplyResultCaseDriverPointIsRankedLow(drvId, applyStatus, applyFailedType, ruleDescription)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | applyStatus                              | ruleDescription   | applyFailedType                                                                 || expectedResult
        1L    | TmsTransportConstant.ApplyStatusEnum.NOT | "ruleDescription" | TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT || null
    }

    @Unroll
    def "setCalculateTimeRangeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        spy.setTransportGroupDriverApplyFailedReasonDTOMap(new HashMap<String, TransportGroupDriverApplyFailedReasonDTO>())
        when:
        def result = spy.setCalculateTimeRange(drvId, calculateTimeFrom, calculateTimeTo)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        calculateTimeTo                           | drvId | calculateTimeFrom                         || expectedResult
        new Timestamp(System.currentTimeMillis()) | 1L    | new Timestamp(System.currentTimeMillis()) || null
    }

    @Unroll
    def "getOrCreateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_, _) >> "getKeyResponse"
        and: "Mock 静态方法"
        mockStatic(TmsTransportConstant.TransportDriverApplyRuleEnum)
        when(TmsTransportConstant.TransportDriverApplyRuleEnum.values()).thenReturn([TmsTransportConstant.TransportDriverApplyRuleEnum.A] as TmsTransportConstant.TransportDriverApplyRuleEnum[])
        when:
        def result = spy.getOrCreate(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result.getDrvId() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || null
    }

    @Unroll
    def "getKeyTest"() {
        given: "设定相关方法入参"
        testObj.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.A)
        when:
        def result = testObj.getKey(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || "A1"
    }

    @Unroll
    def "getKeyTest1"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getKey(rule, drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | rule                                                || expectedResult
        1L    | TmsTransportConstant.TransportDriverApplyRuleEnum.A || "A1"
    }

    @Unroll
    def "logTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        when:
        def result = new TransportGroupDriverApplyProcessDTO().log()

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult     | nouse
        true | true
    }

    @Unroll
    def "convert2TransportGroupDriverRelationListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getApplyFailedReason(_) >> "getApplyFailedReasonResponse"
        spy.getKey(_) >> "getKeyResponse"
        spy.getApplyFailedReasonDetail(_, _) >> "getApplyFailedReasonDetailResponse"
        spy.setTransportGroupDriverApplyFailedReasonDTOMap(new HashMap<String, TransportGroupDriverApplyFailedReasonDTO>())

        when:
        def result = spy.convert2TransportGroupDriverRelationList(eliminateDriverRelationList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        eliminateDriverRelationList                                                               || expectedResult
        [new TspTransportGroupDriverRelationPO(workShiftId: 1L, transportGroupId: 1L, drvId: 1L)] || []
    }

    @Unroll
    def "getApplyFailedReasonDetailTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getApplyFailedReasonDetail(drvDTO, workShiftId)

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDTO                                                                                                                                         | workShiftId || expectedResult
        new TransportGroupDriverApplyFailedReasonDTO(applyFailedType: TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT) | 1L          || true
    }

    @Unroll
    def "getApplyFailedReasonTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getApplyFailedReason(drvDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDTO                                                                           || expectedResult
        new TransportGroupDriverApplyFailedReasonDTO(ruleDescription: "ruleDescription") || "ruleDescription"
    }

    @Unroll
    def "getFailedDriverListTest"() {
        given: "设定相关方法入参"
        testObj.setTransportGroupDriverApplyFailedReasonDTOMap(new HashMap<String, TransportGroupDriverApplyFailedReasonDTO>())
        testObj.setWorkshiftApplySuccessDriverListMap(new HashMap<Long, List<Long>>())
        when:
        def result = testObj.getFailedDriverList()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult | nouse
        []             | true
    }

    @Unroll
    def "setDriverApplyStatusCaseDriverPointTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getApplyDriverMap() >> [(1L): [1L]]
        spy.getOrCreate(_) >> new TransportGroupDriverApplyFailedReasonDTO(driverPoint: 0d)
        spy.setApplyResultCaseDriverPointIsRankedLow(_, _, _, _) >> {}
        spy.getWorkshiftFailedDriverListMap() >> [(1L): [1L]]
        spy.getWorkshiftApplySuccessDriverListMap() >> [(1L): [1L]]
        when:
        def result = spy.setDriverApplyStatusCaseDriverPoint()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult | nouse
        null           | true
    }

    @Unroll
    def "setDriverPointAndWorkShiftIntoApplyProcessTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.setWorkShiftDriverRelationDetailPOMap(_) >> {}
        spy.setApplyDriverMap(_) >> {}
        spy.setDriverPointInfo(_, _, _, _, _) >> {}
        spy.setDriverPointMap(_) >> {}
        when:
        def result = spy.setDriverPointAndWorkShiftIntoApplyProcess(participatingDriversMap, cityRankingDriverPointMap)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        participatingDriversMap                                               | cityRankingDriverPointMap || expectedResult
        [(1L): [new ApplyDriverRelationDetailPO(drvId: 1L, workShiftId: 1L)]] | [null]                    || null
    }

    @Unroll
    def "setDriverApplicationMaximumLimitTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.setDriverApplicationMaximumLimit(workShiftPOSMap, workShiftApplyInfo)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        workShiftPOSMap                                                | workShiftApplyInfo || expectedResult
        [(1L): new TspTransportGroupWorkShiftPO(driverUpperLimit: 1L)] | [(1L): [1L]]       || null
    }

    @Unroll
    def "getTransportGroupDriverApplicationRecordPOListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getOrCreate(_) >> new TransportGroupDriverApplyFailedReasonDTO(drvId: 1L, ruleDescription: "ruleDescription", applyStatus: TmsTransportConstant.ApplyStatusEnum.NOT, applyFailedType: TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT)
        spy.getApplyFailedReasonDetail(_, _) >> "getApplyFailedReasonDetailResponse"
        spy.getWorkshiftApplySuccessDriverListMap() >> [(1L): [1L]]
        when:
        def result = spy.getTransportGroupDriverApplicationRecordPOList(soaRequestType, participatingDriversMap)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        soaRequestType                                                          | participatingDriversMap                                               || expectedResult
        new UpdateTransportGroupApplyStatusSOARequestType(transportGroupId: 1L) | [(1L): [new ApplyDriverRelationDetailPO(drvId: 1L, workShiftId: 1L)]] || []
    }

    @Unroll
    def "removeDriverLeaveAndFreezeDurationNotTriggerThresholdTest"() {
        given: "设定相关方法入参"
        testObj.setTransportGroupDriverApplyFailedReasonDTOMap(new HashMap<String, TransportGroupDriverApplyFailedReasonDTO>())
        when:
        def result = testObj.removeDriverLeaveAndFreezeDurationNotTriggerThreshold()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult | nouse
        null           | true
    }

    @Unroll
    def "getApplySuccessDriverWorkPeriodMapTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDriverWorkPeriodMap() >> [(1L): "String"]
        spy.getWorkshiftApplySuccessDriverListMap() >> [(1L): [1L]]
        when:
        def result = spy.getApplySuccessDriverWorkPeriodMap()

        then: "验证返回结果里属性值是否符合预期"
        result.get(1L) == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult | nouse
        "String"       | true
    }

    def setup() {

        testObj.workshiftApplySuccessDriverListMap = workshiftApplySuccessDriverListMap
        testObj.driverWorkPeriodMap = driverWorkPeriodMap
        testObj.workShiftDriverRelationDetailPOMap = workShiftDriverRelationDetailPOMap
        testObj.transportGroupDriverApplyFailedReasonDTOMap = transportGroupDriverApplyFailedReasonDTOMap
        testObj.workSiftDriverApplicationMaximumLimitMap = workSiftDriverApplicationMaximumLimitMap
        testObj.workshiftFailedDriverListMap = workshiftFailedDriverListMap
        testObj.driverPointMap = driverPointMap
        testObj.applyDriverMap = applyDriverMap
    }

    @Unroll
    def "setLeaveHourTest2"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getKey(_) >> "getKeyResponse"
        when:
        def result = spy.setLeaveHour(drvId, leaveBeginTime, leaveEndTime, duration)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        duration | drvId | leaveBeginTime                            | leaveEndTime                              || expectedResult
        0L       | 1L    | new Timestamp(System.currentTimeMillis()) | new Timestamp(System.currentTimeMillis()) || null
    }
}
