package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;

import java.util.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, TmsDrvLoginInformationRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class TmsDrvLoginInformationRepositoryImplTest {


    @Test
    public void queryDrvLoginInfoLimit() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsDrvLoginInformationRepositoryImpl relationRepository = new TmsDrvLoginInformationRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<TmsDrvLoginInformationPO> list =  relationRepository.queryDrvLoginInfoLimit(1L,1,1);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }
}
