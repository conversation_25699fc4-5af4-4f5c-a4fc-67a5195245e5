package com.ctrip.dcs.tms.transport.interfaces.listener;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.DrvInActiveEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;

import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 * @Description  司机端域消息消费
 **/
@Component
public class DriverDomainListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverDomainListener.class);

    @Autowired
    DriverCommandService commandService;

    @Autowired
    private DrvDrvierRepository repository;

    @Autowired
    private TmsDrvInactiveReasonRepository drvInactiveReasonRepository;

    @Autowired
    private CommonConfig config;

    /**
     * 司机登端
     * @param message
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.DCS_DRIVER_LOGIN_MESSAGE, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void drvLogin(Message message){
        try{
            DriverLoginSuccessMessage data = JsonUtils.fromJson(message.getStringProperty("data"), DriverLoginSuccessMessage.class);
            Long drvId = data.getDriverId();
            DrvDriverPO drvDriverPO = repository.queryByPk(drvId);
            if (drvDriverPO == null) {
                Cat.logEvent(CatEventType.DRIVER_LOGIN, "driver_not_found", "WARN", "drvId:" + drvId);
                return;
            }

            if (!Objects.equals(TmsTransportConstant.DrvStatusEnum.UNACT.getCode(), drvDriverPO.getDrvStatus())) {
                Cat.logEvent(CatEventType.DRIVER_LOGIN, "driver_already_active", "INFO", "drvId:" + drvId + ",status:" + drvDriverPO.getDrvStatus());
                return;
            }

            if (!Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),drvDriverPO.getInternalScope())) {
                Cat.logEvent(CatEventType.DRIVER_LOGIN, "not_overseas_driver", "INFO", "drvId:" + drvId + ",internalScope:" + drvDriverPO.getInternalScope());
                return;
            }

            // 清除未登端的未激活原因
            List<Integer> inactiveReasonCodes = Lists.newArrayList(DrvInActiveEnum.NOT_LOGIN.getCode(), DrvInActiveEnum.ACCOUNT_REGISTER_ERROR.getCode());
            if (config.getLoginTypeList().contains(data.getLoginType())) {
                inactiveReasonCodes.add(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode());
            }
            drvInactiveReasonRepository.deleteReasonByCode(drvId, inactiveReasonCodes, Constant.SYSTEM.toLowerCase());

            // 查询司机是否还有其他未激活原因
            List<TmsDrvInactiveReasonPO> otherReasons = drvInactiveReasonRepository.query(Lists.newArrayList(drvId));

            if (otherReasons == null || otherReasons.isEmpty()) {
                // 没有其他未激活原因，上线司机
                commandService.updateDrvStatus(Lists.newArrayList(drvId), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), "system");
                logger.info("drvLogin", "Driver {} activated successfully, no other inactive reasons found", drvId);
                Cat.logEvent(Constant.EventType.DRIVER, Constant.EventName.LOGIN_ACTIVE);
            } else {
                // 有其他未激活原因，不上线司机
                String reasonCodes = otherReasons.stream().map(reason -> String.valueOf(reason.getReasonCode())).collect(Collectors.joining(","));
                Cat.logEvent(CatEventType.DRIVER_LOGIN, "driver_not_activated", "WARN", "drvId:" + drvId + ",reasons:" + reasonCodes);
                logger.info("drvLogin", "Driver {} not activated, other inactive reasons found: {}", drvId,
                        otherReasons.stream().map(reason -> reason.getReasonCode() + ": " + reason.getReasonDesc())
                                .collect(Collectors.joining(", ")));
            }

        }catch (Exception e){
            logger.error("drvLogin error:{}",e.getLocalizedMessage());
        }
    }

}
