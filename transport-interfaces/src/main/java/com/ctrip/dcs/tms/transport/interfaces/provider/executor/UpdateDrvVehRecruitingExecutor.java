package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.base.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机车辆审批编辑
 */
@Component
public class UpdateDrvVehRecruitingExecutor extends AbstractRpcExecutor<DrvVehRecruitingUpdateSOARequestType, DrvVehRecruitingUpdateSOAResponseType> implements Validator<DrvVehRecruitingUpdateSOARequestType> {

    @Autowired
    private DrvVehRecruitingCommandService commandService;

    @Override
    public DrvVehRecruitingUpdateSOAResponseType execute(DrvVehRecruitingUpdateSOARequestType requestType) {
        DrvVehRecruitingUpdateSOAResponseType responseType = new DrvVehRecruitingUpdateSOAResponseType();
        Result<Boolean> result = commandService.updateDrvVehRecruiting(requestType);
        if (result.isSuccess()) {
            responseType.setMessage(result.getMsg());
            responseType.setHasTip(Strings.isNullOrEmpty(result.getMsg()));
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }
}
