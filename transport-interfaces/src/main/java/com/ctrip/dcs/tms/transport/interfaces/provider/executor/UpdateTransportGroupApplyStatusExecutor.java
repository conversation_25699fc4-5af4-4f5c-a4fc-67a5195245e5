package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Date 2020/11/25 15:12
 */
@Component
public class UpdateTransportGroupApplyStatusExecutor extends AbstractRpcExecutor<UpdateTransportGroupApplyStatusSOARequestType, UpdateTransportGroupApplyStatusSOAResponseType> implements Validator<UpdateTransportGroupApplyStatusSOARequestType> {


    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Override
    public UpdateTransportGroupApplyStatusSOAResponseType execute(UpdateTransportGroupApplyStatusSOARequestType updateTransportGroupApplyStatusSOARequestType) {
        UpdateTransportGroupApplyStatusSOAResponseType responseType = new UpdateTransportGroupApplyStatusSOAResponseType();
        Result<UpdateTransportGroupApplyStatusSOADTO> result = transportGroupCommandService.updateTransportGroupApplyStatus(updateTransportGroupApplyStatusSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<UpdateTransportGroupApplyStatusSOARequestType> validator, UpdateTransportGroupApplyStatusSOARequestType req) {
        validator.ruleFor("transportGroupId").notNull();
        validator.ruleFor("updateType").notNull();
        validator.ruleFor("modifyUser").notNull().notEmpty();
    }
}
