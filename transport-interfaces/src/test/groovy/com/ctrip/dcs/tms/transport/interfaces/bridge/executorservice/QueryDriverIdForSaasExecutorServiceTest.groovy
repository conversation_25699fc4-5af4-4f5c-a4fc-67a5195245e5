package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice

import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverForSaasService
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class QueryDriverIdForSaasExecutorServiceTest extends Specification {
    def testObj = new QueryDriverIdForSaasExecutorService()
    def queryDriverForSaasService = Mock(IQueryDriverForSaasService)

    def setup() {

        testObj.queryDriverForSaasService = queryDriverForSaasService
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        queryDriverForSaasService.queryDriverId(_, _) >> [1L]

        and: "Spy相关接口"
        spy.getResponse(_) >> new QueryDriverIdForSaasResponseType()

        when:
        def result = spy.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"

        result.driverIds == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                  || expectedResult
        new QueryDriverIdForSaasRequestType(driverPhone: ["String"], supplierId: 1L) || null
    }


    @Unroll
    def "getResponseTest"() {

        when:
        def result = testObj.getResponse(driverIds)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverIds || expectedResult
        [1L]      || new QueryDriverIdForSaasResponseType(driverIds: [1L])
    }

}

