package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice

import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class QueryDrvIdByTransportGroupsExecutorServiceTest extends Specification {
    def testObj = new QueryDrvIdByTransportGroupsExecutorService()
    def transportGroupQueryService = Mock(TransportGroupQueryService)

    def setup() {

        testObj.transportGroupQueryService = transportGroupQueryService
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        transportGroupQueryService.queryDrvIdByTransportGroups(_, _) >> [1L]


        when:
        def result = testObj.execute(req)

        then: "验证返回结果里属性值是否符合预期"

        result.drvIdList == expectedResult
        where: "表格方式验证多种分支调用场景"
        req                                                                                              || expectedResult
        new QueryDrvIdByTransportGroupsRequestType(transportGroupIdList: [1L], temporaryDispatchMark: 0) || [1L]
    }

}

