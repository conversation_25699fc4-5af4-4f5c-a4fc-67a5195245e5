package com.ctrip.dcs.tms.transport.interfaces.provider.executor

import base.BaseTest
import com.ctrip.dcs.tms.transport.api.model.QuerySupplierIdBySkuIdRequestType
import com.ctrip.dcs.tms.transport.application.dto.SkuTransportGroupDTO
import com.ctrip.dcs.tms.transport.application.query.IQuerySupplierIdBySkuIdService
import com.ctrip.igt.framework.common.exception.BizException

class QuerySupplierIdBySkuIdExecutorTest extends BaseTest{

    def "handle"(){
        given:
        def service = Mock(IQuerySupplierIdBySkuIdService)
        def executor = new QuerySupplierIdBySkuIdExecutor(querySupplierIdBySkuIdService:service)
        def request = new QuerySupplierIdBySkuIdRequestType("skuIds": Arrays.asList(1L,2L))
        def value1 = new SkuTransportGroupDTO(supplierIdList:Arrays.asList(12L,13L).toSet(),emailList:Arrays.asList("<EMAIL>","<EMAIL>"))
        def value2 = new SkuTransportGroupDTO(supplierIdList:Arrays.asList(22L,23L).toSet(),emailList:Arrays.asList("<EMAIL>","<EMAIL>"))
        def queryResult = new HashMap()
        queryResult.put(1L,value1)
        queryResult.put(2L,value2)
        when:
        service.querySupplierIdBySkuId(_) >> queryResult
        def result = executor.execute(request);
        then:
        result.getSupplierIdList().get(0).getSupplierIdList().contains(12L)
        result.getSupplierIdList().get(0).getEmailList().contains("<EMAIL>")
        result.getSupplierIdList().get(1).getSupplierIdList().contains(22L)
        result.getSupplierIdList().get(1).getEmailList().contains("<EMAIL>")

    }
}
