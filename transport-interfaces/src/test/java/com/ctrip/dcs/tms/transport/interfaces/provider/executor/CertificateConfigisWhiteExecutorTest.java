package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class CertificateConfigisWhiteExecutorTest {

    @InjectMocks
    CertificateConfigisWhiteExecutor executor;
    @Mock
    CertificateQueryService certificateQueryService;

    @Test
    public void execute() {
        CertificateConfigIsWhiteSOARequestType soaRequestType = new CertificateConfigIsWhiteSOARequestType();
        soaRequestType.setSourceId(1L);
        soaRequestType.setSourceType(1);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        Mockito.when(certificateQueryService.certificateConfigIsWhite(soaRequestType)).thenReturn(result);
        CertificateConfigIsWhiteSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
